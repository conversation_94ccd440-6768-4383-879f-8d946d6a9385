
import { supabase } from "@/integrations/supabase/client";
import { Survey, SurveyResponse, SurveyAnswer, FieldType, SupabaseAnswer } from "@/types";

/**
 * Loads a published survey by its ID or share link
 * This function can be used without authentication
 */
export const loadPublicSurvey = async (surveyIdentifier: string): Promise<Survey | null> => {
  try {
    const { data: surveys, error } = await supabase
      .from('sv_surveys')
      .select(`
        id, user_id, title, description, created_at, updated_at, published, share_link, start_date, end_date,
        sv_sections (
          id, survey_id, title, description, order_index, created_at, updated_at,
          sv_fields (
            id, section_id, type, question, description, required, options, placeholder, min, max, order_index, created_at, updated_at
          )
        )
      `)
      .eq('share_link', surveyIdentifier)
      .maybeSingle();
    
    if (error) {
      console.error("Error loading survey:", error);
      return null;
    }

    if (!surveys) {
      console.log("No survey found with share link:", surveyIdentifier);
      return null;
    }

    // Ensure sv_sections is an array
    const sections = Array.isArray(surveys.sv_sections) ? surveys.sv_sections : [];

    // Perform data transformation to match the Survey type
    const survey: Survey = {
      id: surveys.id,
      user_id: surveys.user_id,
      title: surveys.title,
      description: surveys.description,
      created_at: surveys.created_at,
      updated_at: surveys.updated_at,
      published: surveys.published,
      share_link: surveys.share_link,
      start_date: surveys.start_date,
      end_date: surveys.end_date,
      sections: sections.map(section => ({
        id: section.id,
        survey_id: section.survey_id,
        title: section.title,
        description: section.description,
        order_index: section.order_index,
        fields: (Array.isArray(section.sv_fields) ? section.sv_fields : []).map(field => ({
          id: field.id,
          section_id: field.section_id,
          type: field.type as FieldType, // Ensure correct type casting
          question: field.question,
          description: field.description,
          required: field.required,
          options: Array.isArray(field.options) ? field.options : (typeof field.options === 'string' ? JSON.parse(field.options) : []),
          placeholder: field.placeholder,
          min: field.min,
          max: field.max,
          order_index: field.order_index,
          created_at: field.created_at,
          updated_at: field.updated_at,
        })).sort((a, b) => a.order_index - b.order_index),
        created_at: section.created_at,
        updated_at: section.updated_at,
      })).sort((a, b) => a.order_index - b.order_index)
    };
    
    return survey;
  } catch (error) {
    console.error("Error loading public survey:", error);
    throw error;
  }
};

/**
 * Creates a new survey response
 */
export const createSurveyResponse = async (surveyId: string, email?: string, metadata?: any) => {
  try {
    const insertData: any = { 
      survey_id: surveyId, 
      started_at: new Date().toISOString() 
    };
    
    if (email) {
      insertData.respondent_email = email;
    }
    
    if (metadata) {
      insertData.metadata = metadata;
    }
    
    const { data, error } = await supabase
      .from('sv_responses')
      .insert([insertData])
      .select();

    if (error) {
      console.error("Error creating survey response:", error);
      throw error;
    }

    return data ? data[0] : null;
  } catch (error) {
    console.error("Error creating survey response:", error);
    throw error;
  }
};

/**
 * Submits answers for a survey response
 */
export const submitSurveyAnswers = async (
  responseId: string, 
  answers: { fieldId: string; value: string | string[] | boolean | number | null }[]
) => {
  try {
    // Map through the answers and prepare them for insertion
    const answersToInsert = answers.map(answer => {
      let value = answer.value;
      let value_json = null;

      // Convert array values to JSON for certain field types
      if (Array.isArray(value)) {
        value_json = value;
        value = null;
      }

      return {
        response_id: responseId,
        field_id: answer.fieldId,
        value: value !== null ? String(value) : null,
        value_json: value_json,
      };
    });

    const { data, error } = await supabase
      .from('sv_answers')
      .insert(answersToInsert)
      .select();

    if (error) {
      console.error("Error submitting survey answers:", error);
      throw error;
    }

    // Mark the survey as completed
    const { error: updateError } = await supabase
      .from('sv_responses')
      .update({ completed_at: new Date().toISOString() })
      .eq('id', responseId);

    if (updateError) {
      console.error("Error updating survey response with completion timestamp:", updateError);
      throw updateError;
    }

    return data;
  } catch (error) {
    console.error("Error submitting survey answers:", error);
    throw error;
  }
};

/**
 * Loads all responses for a survey
 */
export const loadSurveyResponses = async (surveyId: string): Promise<SurveyResponse[]> => {
  try {
    // Get all responses for the survey
    const { data: responses, error: responsesError } = await supabase
      .from('sv_responses')
      .select(`
        id, survey_id, respondent_id, respondent_email, 
        respondent_ip, started_at, completed_at, metadata
      `)
      .eq('survey_id', surveyId);
    
    if (responsesError) {
      console.error("Error loading survey responses:", responsesError);
      throw responsesError;
    }
    
    if (!responses || responses.length === 0) {
      return [];
    }
    
    // For each response, get all the answers
    const responsesWithAnswers: SurveyResponse[] = await Promise.all(responses.map(async (response) => {
      const { data: answersData, error: answersError } = await supabase
        .from('sv_answers')
        .select(`
          id,
          field_id,
          value,
          value_json,
          created_at,
          sv_fields (
            question,
            type
          )
        `)
        .eq('response_id', response.id);
      
      if (answersError) {
        console.error("Error loading survey answers:", answersError);
        throw answersError;
      }
      
      // Transform the answers to match our SurveyAnswer type
      const transformedAnswers: SurveyAnswer[] = answersData?.map((answer: SupabaseAnswer) => ({
        id: answer.id,
        response_id: response.id,
        field_id: answer.field_id,
        value: answer.value,
        value_json: answer.value_json,
        created_at: answer.created_at
      })) || [];
      
      return {
        ...response,
        // Use the started_at time for sorting in the UI
        created_at: response.started_at,
        answers: transformedAnswers
      };
    }));
    
    return responsesWithAnswers;
  } catch (error) {
    console.error("Error loading survey responses:", error);
    throw error;
  }
};
