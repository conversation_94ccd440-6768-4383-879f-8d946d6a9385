
import { Survey, Section, Field, FieldType } from "../types";
import { TemplateGoal } from "./templateService";

interface GenerateTemplateParams {
  goal: TemplateGoal;
  industry?: string;
  productType?: string;
  eventType?: string;
  researchTopic?: string;
  additionalContext?: string;
}

export const AIService = {
  async generateSurveyTemplate(params: GenerateTemplateParams): Promise<Survey> {
    try {
      // Access environment variables
      const apiKey = "********************************************************************************************************************************************************************";
      if (!apiKey) {
        throw new Error("OpenAI API key not found");
      }
      
      // Create the prompt for AI
      const prompt = createTemplatePrompt(params);
      
      // Call OpenAI API
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4-turbo",
          messages: [
            {
              role: "system",
              content: "You are a survey design expert. Create comprehensive, well-structured surveys with appropriate sections and question types."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          response_format: { type: "json_object" }
        })
      });
      
      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }
      
      const result = await response.json();
      const surveyTemplate = JSON.parse(result.choices[0].message.content);
      console.log(surveyTemplate);
      // Process the returned JSON to match our Survey structure
      return processSurveyTemplate(surveyTemplate);
    } catch (error) {
      console.error("Error calling AI service:", error);
      throw new Error("Failed to generate AI template");
    }
  }
};

// Helper function to create the prompt
function createTemplatePrompt(params: GenerateTemplateParams): string {
  const { goal, industry, productType, eventType, researchTopic, additionalContext } = params;
  
  let prompt = `Create a detailed survey template for ${goalToReadable(goal)}. `;
  
  if (industry) {
    prompt += `The industry is ${industry}. `;
  }
  
  if (productType && goal === "customer_feedback") {
    prompt += `The product type is ${productType}. `;
  }
  
  if (eventType && goal === "event_registration") {
    prompt += `The event type is ${eventType}. `;
  }
  
  if (researchTopic && goal === "research_study") {
    prompt += `The research topic is ${researchTopic}. `;
  }
  
  if (additionalContext) {
    prompt += `Additional context: ${additionalContext}. `;
  }
  
  prompt += `
  Return the result as a JSON object with the following structure:
  {
    "title": "Survey Title",
    "description": "Survey description",
    "sections": [
      {
        "title": "Section Title",
        "description": "Section description",
        "fields": [
          {
            "type": "text|paragraph|multipleChoice|checkbox|dropdown|email|number|date|time|rating",
            "question": "Question text",
            "required": true|false,
            "options": ["Option 1", "Option 2"] // Only for multipleChoice, checkbox, dropdown
          }
        ]
      }
    ]
  }
  
  Supported field types are: text, paragraph, multipleChoice, checkbox, dropdown, email, number, date, time, rating.
  For multipleChoice, checkbox, and dropdown fields, include an 'options' array.
  For rating fields, you can optionally include min, max, minLabel, and maxLabel.
  Create a professional, well-structured survey with appropriate sections and questions.`;
  
  return prompt;
}

function goalToReadable(goal: TemplateGoal): string {
  switch (goal) {
    case "customer_feedback":
      return "customer feedback";
    case "event_registration":
      return "event registration";
    case "research_study":
      return "a research study";
    case "employee_satisfaction":
      return "employee satisfaction";
    case "education_assessment":
      return "education assessment";
    case "product_evaluation":
      return "product evaluation";
    case "conference_feedback":
      return "conference feedback";
    default:
      // Handle any unexpected values by converting underscores to spaces
      return (goal as string).replace(/_/g, " ");
  }
}

// Process the AI's response into our Survey structure
function processSurveyTemplate(template: any): Survey {
  const survey: Survey = {
    id: crypto.randomUUID(),
    user_id: "", // Will be set by SurveyContext
    title: template.title || "Untitled Survey",
    description: template.description || "",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    published: false,
    sections: [],
    responses: 0
  };
  
  // Process sections
  if (template.sections && Array.isArray(template.sections)) {
    survey.sections = template.sections.map((sectionData: any, sectionIndex: number) => {
      const section: Section = {
        id: crypto.randomUUID(),
        survey_id: survey.id,
        title: sectionData.title || `Section ${sectionIndex + 1}`,
        description: sectionData.description || "",
        order_index: sectionIndex,
        fields: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // Process fields
      if (sectionData.fields && Array.isArray(sectionData.fields)) {
        section.fields = sectionData.fields.map((fieldData: any, fieldIndex: number) => {
          // Ensure field type is valid
          const type = validateFieldType(fieldData.type);
          
          const field: Field = {
            id: crypto.randomUUID(),
            section_id: section.id,
            type,
            question: fieldData.question || `Question ${fieldIndex + 1}`,
            description: fieldData.description || "",
            required: !!fieldData.required,
            order_index: fieldIndex,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          
          // Handle options for choice-based fields
          if (["multipleChoice", "checkbox", "dropdown"].includes(type) && 
              fieldData.options && Array.isArray(fieldData.options)) {
            field.options = fieldData.options;
          }
          
          // Handle rating field properties
          if (type === "rating") {
            if (typeof fieldData.min === "number") field.min = fieldData.min;
            if (typeof fieldData.max === "number") field.max = fieldData.max;
            if (fieldData.minLabel) field.minLabel = fieldData.minLabel;
            if (fieldData.maxLabel) field.maxLabel = fieldData.maxLabel;
          }
          
          return field;
        });
      }
      
      return section;
    });
  }
  
  return survey;
}

// Validate and normalize field type
function validateFieldType(type: string): FieldType {
  const validTypes: FieldType[] = [
    "text", "paragraph", "multipleChoice", "checkbox", "dropdown",
    "email", "number", "date", "time", "rating", "multipleRating", "file"
  ];
  
  const normalizedType = type.toLowerCase() as FieldType;
  
  if (validTypes.includes(normalizedType)) {
    return normalizedType;
  }
  
  // Default to text if invalid type
  console.warn(`Invalid field type: ${type}, defaulting to text`);
  return "text";
}
