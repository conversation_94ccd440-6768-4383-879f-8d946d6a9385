import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { SurveyProvider } from "@/context/SurveyContext";
import { AuthProvider } from "@/context/AuthContext";
import ComingSoonPage from "./pages/ComingSoonPage";
import Dashboard from "./pages/Dashboard";
import Create from "./pages/Create";
import Preview from "./pages/Preview";
import NotFound from "./pages/NotFound";
import Auth from "./pages/Auth";
import ProtectedRoute from "./components/layout/ProtectedRoute";
import SharedSurvey from "./pages/SharedSurvey";
import SurveyCompleted from "./pages/SurveyCompleted";
import SurveyResults from "./pages/SurveyResults";


import { AnimatePresence } from "framer-motion";
import Templates from "./pages/Templates";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <SurveyProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AnimatePresence mode="wait">
              <Routes>
                <Route path="/" element={<ComingSoonPage />} />
                <Route path="/auth" element={<Auth />} />
                <Route 
                  path="/dashboard"  
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/templates"  
                  element={
                    <ProtectedRoute>
                      <Templates />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/create" 
                  element={
                    <ProtectedRoute>
                      <Create />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/preview" 
                  element={
                    <ProtectedRoute>
                      <Preview />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/results/:id" 
                  element={
                    <ProtectedRoute>
                      <SurveyResults />
                    </ProtectedRoute>
                  } 
                />
                <Route path="/s/:shareLink" element={<SharedSurvey />} />
                <Route path="/survey-completed" element={<SurveyCompleted />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </AnimatePresence>
          </BrowserRouter>
        </SurveyProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
