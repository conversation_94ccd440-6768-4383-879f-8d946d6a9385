import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Field } from '@/types';
import { Settings, Trash2, ArrowUp, ArrowDown, PlusCircle, GripVertical, X, Star } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MultipleRatingFieldProps {
  field: Field;
  onChange: (updatedField: Field) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

const MultipleRatingField: React.FC<MultipleRatingFieldProps> = ({ 
  field, 
  onChange, 
  onDelete, 
  onMoveUp, 
  onMoveDown 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [newOption, setNewOption] = useState('');
  
  // Parse options from field
  const raw = field.options;
  const options: string[] = (() => {
    if (!raw) return [];
    if (Array.isArray(raw)) return raw;
    try {
      return JSON.parse(raw) as string[];
    } catch {
      console.warn("Invalid JSON in field.options:", raw);
      return [];
    }
  })();

  const handleQuestionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...field,
      question: e.target.value
    });
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange({
      ...field,
      description: e.target.value
    });
  };

  const handleRequiredChange = (checked: boolean) => {
    onChange({
      ...field,
      required: checked
    });
  };

  const handleOptionChange = (index: number, value: string) => {
    if (!field.options) return;
    
    const newOptions = [...field.options];
    newOptions[index] = value;
    
    onChange({
      ...field,
      options: newOptions
    });
  };

  const handleAddOption = () => {
    if (!newOption.trim()) return;
    
    onChange({
      ...field,
      options: [...(field.options || []), newOption]
    });
    
    setNewOption('');
  };

  const handleRemoveOption = (index: number) => {
    if (!field.options) return;
    
    const newOptions = [...field.options];
    newOptions.splice(index, 1);
    
    onChange({
      ...field,
      options: newOptions
    });
  };

  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (isNaN(value)) return;

    onChange({
      ...field,
      min: value,
    });
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (isNaN(value)) return;

    onChange({
      ...field,
      max: value,
    });
  };

  const handleLabelChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "min" | "max"
  ) => {
    onChange({
      ...field,
      [`${type}Label`]: e.target.value,
    });
  };

  return (
    <Card className="border shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start gap-2">
          <div className="text-muted-foreground pt-1.5">
            <GripVertical className="h-5 w-5 cursor-move" />
          </div>
          
          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded-md mr-2">
                    Multiple Rating
                  </span>
                  <Input
                    value={field.question}
                    onChange={handleQuestionChange}
                    placeholder="Enter question text"
                    className="border-none shadow-none focus-visible:ring-0 text-base font-medium p-0 h-auto"
                  />
                </div>
                
                <div className="flex items-center space-x-1">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={onMoveUp}>
                        <ArrowUp className="h-4 w-4 mr-2" />
                        Move Up
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={onMoveDown}>
                        <ArrowDown className="h-4 w-4 mr-2" />
                        Move Down
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive" 
                        onClick={onDelete}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? "Less options" : "More options"}
                  </Button>
                </div>
              </div>
              
              {isExpanded && (
                <div className="space-y-4 pt-2">
                  <div className="space-y-2">
                    <Label htmlFor={`${field.id}-description`}>Description (optional)</Label>
                    <Textarea
                      id={`${field.id}-description`}
                      value={field.description || ''}
                      onChange={handleDescriptionChange}
                      placeholder="Add a description"
                      className="resize-none"
                      rows={2}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`${field.id}-min`}>Min Rating</Label>
                      <Input
                        id={`${field.id}-min`}
                        type="number"
                        value={field.min || 1}
                        onChange={handleMinChange}
                        min="1"
                        max="10"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`${field.id}-max`}>Max Rating</Label>
                      <Input
                        id={`${field.id}-max`}
                        type="number"
                        value={field.max || 5}
                        onChange={handleMaxChange}
                        min="1"
                        max="10"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`${field.id}-min-label`}>Min Label (optional)</Label>
                      <Input
                        id={`${field.id}-min-label`}
                        value={field.minLabel || ''}
                        onChange={(e) => handleLabelChange(e, 'min')}
                        placeholder="e.g., Poor"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`${field.id}-max-label`}>Max Label (optional)</Label>
                      <Input
                        id={`${field.id}-max-label`}
                        value={field.maxLabel || ''}
                        onChange={(e) => handleLabelChange(e, 'max')}
                        placeholder="e.g., Excellent"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="text-sm font-medium">Items to Rate</div>
                {options?.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: field.max || 5 }, (_, i) => (
                        <Star
                          key={i}
                          className="h-4 w-4 text-muted-foreground/30"
                        />
                      ))}
                    </div>
                    <Input
                      value={option}
                      onChange={(e) => handleOptionChange(index, e.target.value)}
                      placeholder="Item to rate"
                      className="flex-1"
                    />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => handleRemoveOption(index)}
                      disabled={field.options?.length === 1}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: field.max || 5 }, (_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 text-muted-foreground/30"
                      />
                    ))}
                  </div>
                  <Input
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    placeholder="Add item to rate"
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newOption.trim()) {
                        e.preventDefault();
                        handleAddOption();
                      }
                    }}
                  />
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleAddOption}
                    disabled={!newOption.trim()}
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-end">
              <div className="flex items-center space-x-2">
                <Label htmlFor={`${field.id}-required`} className="text-sm cursor-pointer">
                  Required
                </Label>
                <Switch
                  id={`${field.id}-required`}
                  checked={field.required}
                  onCheckedChange={handleRequiredChange}
                />
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="text-sm text-muted-foreground mb-2">Preview:</div>
              <div className="space-y-3">
                {options?.map((option, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{option}</span>
                    <div className="flex items-center space-x-2">
                      {field.minLabel && <span className="text-xs text-muted-foreground">{field.minLabel}</span>}
                      <div className="flex items-center">
                        {Array.from({ length: field.max || 5 }, (_, i) => (
                          <Star
                            key={i}
                            className="h-5 w-5 text-muted-foreground/30"
                          />
                        ))}
                      </div>
                      {field.maxLabel && <span className="text-xs text-muted-foreground">{field.maxLabel}</span>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MultipleRatingField;
