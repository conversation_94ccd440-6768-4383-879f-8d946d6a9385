export type FieldType = 'text' | 'paragraph' | 'multipleChoice' | 'checkbox' | 'dropdown' | 'email' | 'number' | 'date' | 'time' | 'rating' | 'file';

export interface Field {
  id: string;
  section_id: string;
  type: FieldType;
  question: string;
  description?: string;
  required: boolean;
  options?: string[];
  placeholder?: string;
  min?: number;
  max?: number;
  minLabel?: string;
  maxLabel?: string;
  order_index: number;
  created_at?: string;
  updated_at?: string;
}

export interface Section {
  id: string;
  survey_id: string;
  title: string;
  description?: string;
  order_index: number;
  fields: Field[];
  created_at?: string;
  updated_at?: string;
}

export interface Survey {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  published: boolean;
  share_link?: string;
  start_date?: string;
  end_date?: string;
  sections: Section[];
  responses?: number;
}

export interface SurveyResponse {
  id: string;
  survey_id: string;
  respondent_id?: string;
  respondent_email?: string;
  respondent_ip?: string;
  started_at: string;
  completed_at?: string;
  metadata?: any;
  answers?: SurveyAnswer[];
}

export interface SupabaseAnswer {
  id: string;
  field_id: string;
  value?: string;
  value_json?: any;
  created_at: string;
  sv_fields?: {
    question: string;
    type: string;
  };
}

export interface SurveyAnswer {
  id: string;
  response_id: string;
  field_id: string;
  value?: string;
  value_json?: any;
  created_at?: string;
}

export type User = {
  id: string;
  email: string;
  name?: string;
};
