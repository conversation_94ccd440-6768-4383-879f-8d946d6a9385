# Technology Context

## Frontend Stack
The application uses:

- **React**: Core UI library
- **TypeScript**: Type-safe JavaScript
- **React Router**: For client-side routing
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Animation library
- **Lucide Icons**: Icon library
- **Sonner**: Toast notification library
- **React Query**: For data fetching, caching, and state management
- **React Hook Form**: For form handling and validation
- **Zod**: For schema validation

## Backend Services
- **Supabase**: Backend-as-a-Service platform providing:
  - PostgreSQL database
  - Authentication and user management
  - Real-time subscriptions
  - Row-level security

## UI Component Library
The application uses shadcn/ui components such as:
- Button, Input, Textarea
- Card (CardContent, CardFooter, CardHeader)
- Dialog (DialogContent, DialogHeader, etc.)
- DropdownMenu
- Accordion
- Badge
- Switch
- Label
- Toast
- Popover

## State Management
- React Context API for global state management (AuthContext, SurveyContext)
- React Query for server state management
- Local component state with useState for UI state

## Development Tools
- **Vite**: For fast development and optimized production builds
- **ESLint**: For code linting
- **TypeScript**: For static type checking
- **Path aliasing**: For cleaner imports (@/components)
- **TailwindCSS**: For styling with utility classes

## Required Setup
To work on this project, a developer would need:
- Node.js environment
- Package manager (npm, yarn, or pnpm)
- Supabase account for backend services
- IDE with TypeScript support
- Git for version control

## Directory Structure
```
project-root/
├── src/
│   ├── components/
│   │   ├── ui/ (shadcn/ui components)
│   │   └── layout/ (Layout components)
│   │   └── surveys/ (Survey-related components)
│   │       └── field-types/ (Different field type components)
│   ├── context/
│   │   ├── AuthContext.tsx
│   │   └── SurveyContext.tsx
│   ├── hooks/
│   │   └── Custom React hooks
│   ├── integrations/
│   │   └── supabase/ (Supabase client configuration)
│   ├── lib/
│   │   └── Utility functions
│   ├── pages/
│   │   ├── Dashboard.tsx
│   │   ├── Create.tsx
│   │   ├── Auth.tsx
│   │   ├── SharedSurvey.tsx
│   │   ├── SurveyResults.tsx
│   │   └── Other page components
│   ├── services/
│   │   └── API service methods
│   └── types.ts (Type definitions)
└── supabase/
    └── Database schema and functions
```

## External APIs
No clear evidence of external APIs in the examined code, but potential integration points:
- Authentication service
- Data storage (likely a backend service or BaaS)
- Analytics integration 