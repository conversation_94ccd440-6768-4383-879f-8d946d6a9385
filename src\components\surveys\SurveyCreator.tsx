import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  PlusCircle, Save, ExternalLink, Trash2, Check, ArrowDown, ArrowUp, 
  LayoutGrid, GripVertical, Settings, Edit, Copy, ChevronDown, Share2
} from 'lucide-react';
import { useSurvey } from '@/context/SurveyContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { 
  Dialog, DialogContent, DialogDescription, DialogFooter, 
  DialogHeader, DialogTitle, DialogTrigger 
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Field, FieldType, Section } from '@/types';
import FieldTypeSelector from './field-types/FieldTypeSelector';
import TextField from './field-types/TextField';
import MultipleChoiceField from './field-types/MultipleChoiceField';
import CheckboxField from './field-types/CheckboxField';
import RatingField from './field-types/RatingField';

const SurveyCreator: React.FC = () => {
  // ==========================================
  // Hooks and State Management
  // ==========================================
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const surveyId = queryParams.get('id');
  
  const {
    surveys,
    currentSurvey,
    setCurrentSurvey,
    createSurvey,
    updateSurvey,
    deleteSurvey,
    publishSurvey,
    unpublishSurvey,
    addSection,
    updateSection,
    deleteSection,
    addField,
    updateField,
    deleteField,
    moveSectionUp,
    moveSectionDown,
    moveFieldUp,
    moveFieldDown,
    generateShareLink,
    fetchSurveyById,
    isLoading
  } = useSurvey();

  // State for survey details
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  
  // State for section management
  const [isAddingSectionOpen, setIsAddingSectionOpen] = useState(false);
  const [newSectionTitle, setNewSectionTitle] = useState('');
  const [newSectionDescription, setNewSectionDescription] = useState('');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activeAccordion, setActiveAccordion] = useState<string[]>([]);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [editingSectionTitle, setEditingSectionTitle] = useState('');
  const [editingSectionDescription, setEditingSectionDescription] = useState('');

  // ==========================================
  // Effects and Data Loading
  // ==========================================
  useEffect(() => {
    const loadSurvey = async () => {
      if (surveyId) {
        // First try to find the survey in the already loaded surveys
        const existingSurvey = surveys.find(s => s.id === surveyId);
        if (existingSurvey) {
          setCurrentSurvey(existingSurvey);
          setTitle(existingSurvey.title);
          setDescription(existingSurvey.description || '');
          setActiveAccordion(existingSurvey.sections.map(section => section.id));
          return;
        }

        // If not found in loaded surveys and not currently loading, fetch it directly
        if (!isLoading) {
          try {
            const survey = await fetchSurveyById(surveyId);
            if (survey) {
              setCurrentSurvey(survey);
              setTitle(survey.title);
              setDescription(survey.description || '');
              setActiveAccordion(survey.sections.map(section => section.id));
            } else {
              // Survey doesn't exist or user doesn't have access
              navigate('/dashboard');
            }
          } catch (error) {
            console.error('Error loading survey:', error);
            navigate('/dashboard');
          }
        }
      } else {
        setTitle('');
        setDescription('');
        setCurrentSurvey(null);
      }
    };

    loadSurvey();
  }, [surveyId, surveys, setCurrentSurvey, navigate, fetchSurveyById, isLoading]);

  // ==========================================
  // Event Handlers - Survey Management
  // ==========================================
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);
  };

  const handleSaveSurvey = async () => {
    if (!title.trim()) {
      toast.error("Please provide a survey title");
      return;
    }

    if (currentSurvey) {
      await updateSurvey({
        ...currentSurvey,
        title,
        description,
      });
    } else {
      const newSurvey = await createSurvey(title, description);
      navigate(`/create?id=${newSurvey.id}`);
    }
  };

  const handleDeleteSurvey = () => {
    if (currentSurvey) {
      deleteSurvey(currentSurvey.id);
      setIsDeleteDialogOpen(false);
      navigate('/dashboard');
    }
  };

  const handlePublishChange = (checked: boolean) => {
    if (!currentSurvey) return;
    
    if (checked) {
      publishSurvey(currentSurvey.id);
    } else {
      unpublishSurvey(currentSurvey.id);
    }
  };

  // ==========================================
  // Event Handlers - Section Management
  // ==========================================
  const handleCreateSection = () => {
    if (!currentSurvey) {
      toast.error("Please save the survey first");
      return;
    }

    if (!newSectionTitle.trim()) {
      toast.error("Please provide a section title");
      return;
    }

    addSection(currentSurvey.id, newSectionTitle, newSectionDescription);
    setNewSectionTitle('');
    setNewSectionDescription('');
    setIsAddingSectionOpen(false);
  };

  const handleSaveSection = (sectionId: string) => {
    if (!currentSurvey) return;
    
    const section = currentSurvey.sections.find(s => s.id === sectionId);
    if (!section) return;
    
    if (!editingSectionTitle.trim()) {
      toast.error("Section title cannot be empty");
      return;
    }
    
    updateSection(currentSurvey.id, {
      ...section,
      title: editingSectionTitle,
      description: editingSectionDescription
    });
    
    setEditingSectionId(null);
  };

  const handleEditSection = (section: Section) => {
    setEditingSectionId(section.id);
    setEditingSectionTitle(section.title);
    setEditingSectionDescription(section.description || '');
  };

  // ==========================================
  // Event Handlers - Field Management
  // ==========================================
  const handleAddField = (sectionId: string, type: FieldType) => {
    if (!currentSurvey) return;
    
    addField(currentSurvey.id, sectionId, type, `New ${type} question`);
  };

  const handleFieldChange = (sectionId: string, updatedField: Field) => {
    if (!currentSurvey) return;
    
    updateField(currentSurvey.id, sectionId, updatedField);
  };

  const handleDeleteField = (sectionId: string, fieldId: string) => {
    if (!currentSurvey) return;
    
    deleteField(currentSurvey.id, sectionId, fieldId);
  };

  // ==========================================
  // Field Rendering Logic
  // ==========================================
  const renderField = (field: Field, sectionId: string) => {
    switch (field.type) {
      case 'text':
      case 'email':
      case 'number':
      case 'date':
      case 'time':
      case 'paragraph':
        return (
          <TextField
            field={field}
            onChange={(updatedField) => handleFieldChange(sectionId, updatedField)}
            onDelete={() => handleDeleteField(sectionId, field.id)}
            onMoveUp={() => moveFieldUp(currentSurvey!.id, sectionId, field.id)}
            onMoveDown={() => moveFieldDown(currentSurvey!.id, sectionId, field.id)}
          />
        );
      case 'multipleChoice':
        return (
          <MultipleChoiceField
            field={field}
            onChange={(updatedField) => handleFieldChange(sectionId, updatedField)}
            onDelete={() => handleDeleteField(sectionId, field.id)}
            onMoveUp={() => moveFieldUp(currentSurvey!.id, sectionId, field.id)}
            onMoveDown={() => moveFieldDown(currentSurvey!.id, sectionId, field.id)}
          />
        );
      case 'checkbox':
        return (
          <CheckboxField
            field={field}
            onChange={(updatedField) => handleFieldChange(sectionId, updatedField)}
            onDelete={() => handleDeleteField(sectionId, field.id)}
            onMoveUp={() => moveFieldUp(currentSurvey!.id, sectionId, field.id)}
            onMoveDown={() => moveFieldDown(currentSurvey!.id, sectionId, field.id)}
          />
        );
      case 'rating':
        return (
          <RatingField
            field={field}
            onChange={(updatedField) => handleFieldChange(sectionId, updatedField)}
            onDelete={() => handleDeleteField(sectionId, field.id)}
            onMoveUp={() => moveFieldUp(currentSurvey!.id, sectionId, field.id)}
            onMoveDown={() => moveFieldDown(currentSurvey!.id, sectionId, field.id)}
          />
        );
      default:
        return (
          <div className="bg-muted p-4 rounded-md">
            <p>Field type {field.type} not implemented yet</p>
          </div>
        );
    }
  };

  // ==========================================
  // Utility Functions
  // ==========================================
  const handleGenerateShareLink = () => {
    if (!currentSurvey) return;
    generateShareLink(currentSurvey.id);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Link copied to clipboard!");
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return '';
    }
  };

  // ==========================================
  // Main Component Render
  // ==========================================

  // Show loading state when fetching a specific survey
  if (surveyId && isLoading && !currentSurvey) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-muted-foreground">Loading survey...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl animate-fade-in">
      <div className="flex flex-col space-y-6">
        {/* ==========================================
             Header Section
             - Title and last updated info
             - Action buttons (Preview, Delete, Save)
             ========================================== */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
          <div className="flex flex-col w-full md:w-auto">
            <h1 className="text-2xl font-bold">
              {currentSurvey ? "Edit Survey" : "Create New Survey"}
            </h1>
            <p className="text-muted-foreground text-sm">
              {currentSurvey 
                ? `Last updated ${formatDate(currentSurvey.updated_at)}`
                : "Design your survey with sections and fields"
              }
            </p>
          </div>
          
          <div className="flex items-center space-x-2 w-full md:w-auto">
            {currentSurvey && (
              <>
                {/* Preview Button */}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => navigate(`/preview?id=${currentSurvey.id}`)}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                
                {/* Delete Survey Dialog */}
                <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Delete Survey</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to delete this survey? This action cannot be undone.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button variant="destructive" onClick={handleDeleteSurvey}>
                        Delete Survey
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </>
            )}
            
            {/* Save Button */}
            <Button variant="default" onClick={handleSaveSurvey}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
        
        {/* ==========================================
             Survey Details Card
             - Survey title and description inputs
             - Publish toggle
             - Share link section (when published)
             ========================================== */}
        <Card className="w-full">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium">Survey Details</h2>
              {currentSurvey && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground mr-2">Publish</span>
                  <Switch 
                    checked={currentSurvey.published} 
                    onCheckedChange={handlePublishChange}
                  />
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Survey Title Input */}
            <div className="space-y-2">
              <Label htmlFor="survey-title">Survey Title</Label>
              <Input
                id="survey-title"
                placeholder="Enter survey title"
                value={title}
                onChange={handleTitleChange}
                className="w-full"
              />
            </div>

            {/* Survey Description Input */}
            <div className="space-y-2">
              <Label htmlFor="survey-description">Description (optional)</Label>
              <Textarea
                id="survey-description"
                placeholder="Enter a description for your survey"
                value={description}
                onChange={handleDescriptionChange}
                className="w-full"
                rows={3}
              />
            </div>
            
            {/* Share Link Section - Only shown when survey is published */}
            {currentSurvey && currentSurvey.published && (
              <div className="pt-4 border-t">
                <div className="flex flex-col space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Public Share Link</Label>
                    {!currentSurvey.share_link && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={handleGenerateShareLink}
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        Generate Link
                      </Button>
                    )}
                     {currentSurvey.share_link && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={handleGenerateShareLink}
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        ReGenerate Link
                      </Button>
                    )}
                  </div>
                  
                  {currentSurvey.share_link ? (
                    <div className="flex items-center mt-1 bg-muted rounded-md p-2">
                      <p className="text-sm text-foreground truncate flex-1">
                        {`${window.location.origin}/s/${currentSurvey.share_link}`}
                      </p>
                      <Button 
                        size="icon"
                        variant="ghost" 
                        onClick={() => copyToClipboard(`${window.location.origin}/s/${currentSurvey.share_link}`)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No share link generated yet.
                    </p>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* ==========================================
             Sections Management
             - Section list with accordion
             - Add section dialog
             - Empty state when no sections
             ========================================== */}
        {currentSurvey && (
          <div className="space-y-4">
            {/* Section Header with Add Button */}
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium">Sections</h2>
              <Dialog open={isAddingSectionOpen} onOpenChange={setIsAddingSectionOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Section
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Section</DialogTitle>
                    <DialogDescription>
                      Create a new section for your survey
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-2">
                    <div className="space-y-2">
                      <Label htmlFor="section-title">Section Title</Label>
                      <Input
                        id="section-title"
                        placeholder="Enter section title"
                        value={newSectionTitle}
                        onChange={(e) => setNewSectionTitle(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="section-description">Description (optional)</Label>
                      <Textarea
                        id="section-description"
                        placeholder="Enter section description"
                        value={newSectionDescription}
                        onChange={(e) => setNewSectionDescription(e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="secondary" onClick={() => setIsAddingSectionOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateSection}>
                      Add Section
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            
            {/* Empty State - No Sections */}
            {currentSurvey.sections.length === 0 ? (
              <Card className="border border-dashed p-8">
                <div className="flex flex-col items-center justify-center text-center space-y-3">
                  <LayoutGrid className="h-12 w-12 text-muted-foreground/50" />
                  <div>
                    <h3 className="text-lg font-medium">No sections yet</h3>
                    <p className="text-sm text-muted-foreground">
                      Add a section to start building your survey
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsAddingSectionOpen(true)}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Section
                  </Button>
                </div>
              </Card>
            ) : (
           
              <Accordion 
                type="multiple" 
                value={activeAccordion} 
                onValueChange={setActiveAccordion}
                className="space-y-4"
              >
                {currentSurvey.sections.map((section, index) => (
                  <AccordionItem 
                    key={section.id} 
                    value={section.id}
                    className="border rounded-lg overflow-hidden"
                  >
                    {/* Section Header */}
                    <div className="border-b bg-card">
                      <div className="flex items-center p-4">
                        {/* Drag Handle */}
                        <div className="mr-2 text-muted-foreground">
                          <GripVertical className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          {/* Section Edit Mode */}
                          {editingSectionId === section.id ? (
                            <div className="space-y-2">
                              <Input
                                value={editingSectionTitle}
                                onChange={(e) => setEditingSectionTitle(e.target.value)}
                                placeholder="Section title"
                                className="w-full"
                              />
                              <Textarea
                                value={editingSectionDescription}
                                onChange={(e) => setEditingSectionDescription(e.target.value)}
                                placeholder="Section description (optional)"
                                rows={2}
                                className="w-full"
                              />
                              <div className="flex justify-end space-x-2">
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  onClick={() => setEditingSectionId(null)}
                                >
                                  Cancel
                                </Button>
                                <Button 
                                  variant="default" 
                                  size="sm"
                                  onClick={() => handleSaveSection(section.id)}
                                >
                                  <Check className="h-4 w-4 mr-1" />
                                  Save
                                </Button>
                              </div>
                            </div>
                          ) : (
                           
                            <AccordionTrigger className="hover:no-underline py-0">
                              <div className="flex flex-col items-start text-left">
                                <div className="flex items-center">
                                  <span className="text-sm font-medium mr-2">
                                    Section {index + 1}
                                  </span>
                                  <span className="font-medium">{section.title}</span>
                                </div>
                                {section.description && (
                                  <p className="text-sm text-muted-foreground mt-1 max-w-md truncate">
                                    {section.description}
                                  </p>
                                )}
                              </div>
                            </AccordionTrigger>
                          )}
                        </div>
                        
                        {/* Section Actions */}
                        {editingSectionId !== section.id && (
                          <div className="flex items-center space-x-1">
                            {/* Edit Button */}
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditSection(section);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            {/* Section Options Dropdown */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <Settings className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {/* Move Up/Down Options */}
                                <DropdownMenuItem 
                                  onClick={() => {
                                    if (index > 0) {
                                      moveSectionUp(currentSurvey.id, section.id);
                                    }
                                  }}
                                  disabled={index === 0}
                                >
                                  <ArrowUp className="h-4 w-4 mr-2" />
                                  Move Up
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => {
                                    if (index < currentSurvey.sections.length - 1) {
                                      moveSectionDown(currentSurvey.id, section.id);
                                    }
                                  }}
                                  disabled={index === currentSurvey.sections.length - 1}
                                >
                                  <ArrowDown className="h-4 w-4 mr-2" />
                                  Move Down
                                </DropdownMenuItem>
                                {/* Duplicate Option */}
                                <DropdownMenuItem 
                                  onClick={() => {
                                    const newSection = {
                                      ...section,
                                      id: generateId(),
                                      title: `${section.title} (Copy)`,
                                      fields: section.fields.map(field => ({
                                        ...field,
                                        id: generateId()
                                      }))
                                    };
                                    
                                    function generateId() {
                                      return Math.random().toString(36).substring(2, 9);
                                    }
                                    
                                    updateSurvey({
                                      ...currentSurvey,
                                      sections: [
                                        ...currentSurvey.sections.slice(0, index + 1),
                                        newSection,
                                        ...currentSurvey.sections.slice(index + 1)
                                      ]
                                    });
                                  }}
                                >
                                  <Copy className="h-4 w-4 mr-2" />
                                  Duplicate
                                </DropdownMenuItem>
                                {/* Delete Option */}
                                <DropdownMenuItem 
                                  className="text-destructive focus:text-destructive" 
                                  onClick={() => deleteSection(currentSurvey.id, section.id)}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                            
                            {/* Expand/Collapse Button */}
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="text-primary"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (activeAccordion.includes(section.id)) {
                                  setActiveAccordion(activeAccordion.filter(id => id !== section.id));
                                } else {
                                  setActiveAccordion([...activeAccordion, section.id]);
                                }
                              }}
                            >
                              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${
                                activeAccordion.includes(section.id) ? "transform rotate-180" : ""
                              }`} />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Section Content */}
                    <AccordionContent className="p-4 pt-2 bg-background/50 space-y-4">
                      {/* Fields List */}
                      {section.fields.length > 0 ? (
                        <div className="space-y-4">
                          {section.fields.map((field) => (
                            <div key={field.id} className="rounded-md">
                              {renderField(field, section.id)}
                            </div>
                          ))}
                        </div>
                      ) : (
                     
                        <div className="text-center py-8 border border-dashed rounded-md">
                          <p className="text-muted-foreground">No fields in this section yet</p>
                        </div>
                      )}
                      
                      {/* Field Type Selector */}
                      <FieldTypeSelector onSelectType={(type) => handleAddField(section.id, type)} />
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SurveyCreator;
