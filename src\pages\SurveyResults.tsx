
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSurvey } from '@/context/SurveyContext';
import { loadSurveyResponses } from '@/services/surveyResponseService';
import { Survey, SurveyResponse } from '@/types';
import Navbar from '@/components/layout/Navbar';
import PageTransition from '@/components/layout/PageTransition';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { ArrowLeft, BarChart2, FileText, User } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import ResponsesList from '@/components/surveys/ResponsesList';
import ResponseAnalytics from '@/components/surveys/ResponseAnalytics';

const SurveyResults = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { fetchSurveyById } = useSurvey();
  const { toast } = useToast();
  
  const [survey, setSurvey] = useState<Survey | null>(null);
  const [responses, setResponses] = useState<SurveyResponse[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<'list' | 'analytics'>('list');

  useEffect(() => {
    const loadData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Load survey details
        const surveyData = await fetchSurveyById(id);
        if (!surveyData) {
          toast({
            title: "Survey not found",
            description: "The requested survey could not be found.",
            variant: "destructive"
          });
          navigate('/dashboard');
          return;
        }

        setSurvey(surveyData);

        // Load survey responses
        try {
          const responsesData = await loadSurveyResponses(id);
          setResponses(responsesData);
        } catch (responseError) {
          console.error("Error loading survey responses:", responseError);
          toast({
            title: "Error loading responses",
            description: "There was a problem loading the survey responses.",
            variant: "destructive"
          });
          // Continue with empty responses array rather than completely failing
          setResponses([]);
        }
      } catch (error) {
        console.error("Error loading survey results:", error);
        toast({
          title: "Error loading results",
          description: "There was a problem loading the survey results.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, fetchSurveyById, navigate]);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />
      <PageTransition>
        <main className="flex-1 container max-w-6xl px-4 py-8 mx-auto">
          <div className="flex items-center mb-6">
            <Button 
              variant="ghost" 
              onClick={() => navigate('/dashboard')}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
          
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-3/4" />
              <Skeleton className="h-6 w-1/2" />
              <Skeleton className="h-[400px] w-full mt-6" />
            </div>
          ) : (
            <>
              <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">{survey?.title}</h1>
                <p className="text-muted-foreground">{survey?.description}</p>
                
                <div className="flex items-center mt-4 gap-4">
                  <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center">
                    <FileText className="h-4 w-4 mr-1" />
                    {responses.length} Responses
                  </div>
                  
                  {survey?.published ? (
                    <div className="bg-green-500/10 text-green-500 px-3 py-1 rounded-full text-sm">
                      Published
                    </div>
                  ) : (
                    <div className="bg-yellow-500/10 text-yellow-500 px-3 py-1 rounded-full text-sm">
                      Draft
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex mb-6 border-b">
                <Button
                  variant={activeTab === 'list' ? 'default' : 'ghost'}
                  onClick={() => setActiveTab('list')}
                  className={`rounded-none border-b-2 ${activeTab === 'list' ? 'border-primary' : 'border-transparent'} px-4 py-2`}
                >
                  <User className="h-4 w-4 mr-2" />
                  Individual Responses
                </Button>
                <Button
                  variant={activeTab === 'analytics' ? 'default' : 'ghost'}
                  onClick={() => setActiveTab('analytics')}
                  className={`rounded-none border-b-2 ${activeTab === 'analytics' ? 'border-primary' : 'border-transparent'} px-4 py-2`}
                >
                  <BarChart2 className="h-4 w-4 mr-2" />
                  Analytics
                </Button>
              </div>
              
              {activeTab === 'list' ? (
                <ResponsesList responses={responses} survey={survey} />
              ) : (
                <ResponseAnalytics responses={responses} survey={survey} />
              )}
            </>
          )}
        </main>
      </PageTransition>
    </div>
  );
};

export default SurveyResults;
