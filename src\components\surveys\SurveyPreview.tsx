import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSurvey } from '@/context/SurveyContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import { toast } from 'sonner';
import { Field } from '@/types';

const SurveyPreview: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const surveyId = queryParams.get('id');
  
  const { surveys, setCurrentSurvey } = useSurvey();
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [formResponses, setFormResponses] = useState<Record<string, any>>({});
  
  useEffect(() => {
    if (surveyId) {
      const survey = surveys.find(s => s.id === surveyId);
      if (survey) {
        setCurrentSurvey(survey);
      } else {
        navigate('/dashboard');
      }
    } else {
      navigate('/dashboard');
    }
  }, [surveyId, surveys, setCurrentSurvey, navigate]);
  
  const survey = surveys.find(s => s.id === surveyId);
  
  if (!survey) {
    return <div className="flex justify-center items-center h-96">Loading...</div>;
  }
  
  const sections = survey.sections;
  const currentSection = sections[currentSectionIndex];
  
  const handlePrevious = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(currentSectionIndex - 1);
      window.scrollTo(0, 0);
    }
  };
  
  const handleNext = () => {
    const requiredFields = currentSection.fields.filter(field => field.required);
    const missingFields = requiredFields.filter(field => !formResponses[field.id]);
    
    if (missingFields.length > 0) {
      toast.error('Please answer all required questions');
      return;
    }
    
    if (currentSectionIndex < sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
      window.scrollTo(0, 0);
    } else {
      toast.success('Survey completed! Thank you for your responses.');
      // In a real implementation, this is where you would submit the form responses to a backend
      console.log('Survey responses:', formResponses);
      
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    }
  };
  
  const updateResponse = (fieldId: string, value: any) => {
    setFormResponses({
      ...formResponses,
      [fieldId]: value
    });
  };
  
  const renderField = (field: Field) => {
    switch (field.type) {
      case 'number':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
              </Label>
            <Input
              id={field.id}
              type="number"
              placeholder={field.placeholder}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
            />
          </div>
        );
        
      case 'text':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <Input
              id={field.id}
              placeholder={field.placeholder}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
            />
          </div>
        );
        
      case 'email':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <Input
              id={field.id}
              type="email"
              placeholder={field.placeholder || '<EMAIL>'}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
            />
          </div>
        );
        
      case 'paragraph':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <Textarea
              id={field.id}
              placeholder={field.placeholder}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
              rows={4}
            />
          </div>
        );
        
      case 'multipleChoice':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <RadioGroup
              value={formResponses[field.id] || ''}
              onValueChange={(value) => updateResponse(field.id, value)}
              className="space-y-2"
            >
              {field.options?.map((option, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`${field.id}-option-${i}`} />
                  <Label htmlFor={`${field.id}-option-${i}`} className="cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        );
        
      case 'checkbox':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <div className="space-y-2">
              {field.options?.map((option, i) => {
                const checkboxValues = formResponses[field.id] || [];
                const isChecked = checkboxValues.includes(option);
                
                return (
                  <div key={i} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${field.id}-option-${i}`}
                      checked={isChecked}
                      onCheckedChange={(checked) => {
                        const currentValues = formResponses[field.id] || [];
                        let newValues;
                        
                        if (checked) {
                          newValues = [...currentValues, option];
                        } else {
                          newValues = currentValues.filter((val: string) => val !== option);
                        }
                        
                        updateResponse(field.id, newValues);
                      }}
                    />
                    <Label htmlFor={`${field.id}-option-${i}`} className="cursor-pointer">
                      {option}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>
        );
        
      case 'rating':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <div className="flex items-center space-x-4">
            {field.minLabel && <span>{field.minLabel}</span>}
              <div className="flex items-center">
                {Array.from({ length: field.max || 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`h-6 w-6 cursor-pointer ${
                      i < (formResponses[field.id] || 0)
                        ? 'text-yellow-400'
                        : 'text-muted-foreground/30'
                    }`}
                    onClick={() => updateResponse(field.id, i + 1)}
                  />
                ))}
              </div>
              {field.maxLabel && <span>{field.maxLabel}</span>}
              
            </div>
          </div>
        );
        
      default:
        return (
          <div className="space-y-2">
            <p>{field.question}</p>
            <p className="text-sm text-muted-foreground">
              Field type '{field.type}' is not implemented in the preview
            </p>
          </div>
        );
    }
  };
  
  return (
    <div className="container mx-auto p-4 max-w-3xl animate-fade-in">
      <div className="flex mb-6 justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{survey.title}</h1>
          {survey.description && (
            <p className="text-muted-foreground mt-1">{survey.description}</p>
          )}
        </div>
        <Button variant="outline" onClick={() => navigate('/dashboard')}>
          Exit Preview
        </Button>
      </div>
      
      <div className="mb-4 w-full bg-muted rounded-full h-2">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300"
          style={{ width: `${((currentSectionIndex + 1) / sections.length) * 100}%` }}
        ></div>
      </div>
      
      <div className="mb-4 flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Section {currentSectionIndex + 1} of {sections.length}
        </p>
        <p className="text-sm font-medium">
          {currentSection.title}
        </p>
      </div>
      
      <Card className="w-full mb-6 border shadow-sm">
        <CardHeader>
          <CardTitle>{currentSection.title}</CardTitle>
          {currentSection.description && (
            <CardDescription>{currentSection.description}</CardDescription>
          )}
        </CardHeader>
        <CardContent className="space-y-6">
          {currentSection.fields.length === 0 ? (
            <p className="text-center text-muted-foreground py-4">
              No questions in this section
            </p>
          ) : (
            currentSection.fields.map((field) => (
              <div key={field.id} className="py-2">
                {renderField(field)}
              </div>
            ))
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentSectionIndex === 0}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={handleNext}>
            {currentSectionIndex === sections.length - 1 ? 'Finish' : 'Next'}
            {currentSectionIndex !== sections.length - 1 && (
              <ChevronRight className="ml-2 h-4 w-4" />
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SurveyPreview;
