# System Patterns

## Architecture Overview
The application appears to follow a React-based frontend architecture with the following key components:

- **React Components**: UI components for different parts of the application
- **Context API**: For state management across components
- **React Router**: For navigation between different views
- **Custom Hooks**: For encapsulating reusable logic

## Key Design Patterns

### Component Structure
The application employs a hierarchical component structure:
- Container components (like SurveyCreator) manage state and logic
- Specialized components handle specific functionality (field types, section management)
- UI components provide reusable visual elements

### Context-Based State Management
- SurveyContext provides centralized state management for surveys
- Actions for creating, updating, and deleting surveys are exposed through the context
- Components connect to the context using the useSurvey hook

### Field Type System
- Abstract field type interface with specialized implementations
- Common field properties with type-specific extensions
- Dedicated components for rendering and editing each field type

### Component Communication
- Top-down prop passing for component configuration
- Callback functions for notifying parent components about changes
- Context API for cross-component state sharing

## Data Model

### Survey Structure
```
Survey
├── id: string
├── title: string
├── description: string
├── published: boolean
├── share_link: string
├── created_at: string
├── updated_at: string
└── sections: Section[]
```

### Section Structure
```
Section
├── id: string
├── title: string
├── description: string
└── fields: Field[]
```

### Field Structure
```
Field
├── id: string
├── type: FieldType
├── label: string
├── required: boolean
├── properties: object (type-specific properties)
└── options: array (for choice-based fields)
```

## UI Patterns
- Card-based design for organizing content
- Accordion components for collapsible sections
- Responsive layout with mobile considerations
- Modals for focused interaction (adding sections, confirmation)
- Dropdown menus for action grouping
- Consistent action button placement 