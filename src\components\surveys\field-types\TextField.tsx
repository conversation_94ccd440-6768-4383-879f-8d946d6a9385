
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Field } from '@/types';
import { Settings, Trash2, ArrowUp, ArrowDown, GripVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TextFieldProps {
  field: Field;
  onChange: (updatedField: Field) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

const TextField: React.FC<TextFieldProps> = ({ 
  field, 
  onChange, 
  onDelete, 
  onMoveUp, 
  onMoveDown 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleQuestionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...field,
      question: e.target.value
    });
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange({
      ...field,
      description: e.target.value
    });
  };

  const handlePlaceholderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...field,
      placeholder: e.target.value
    });
  };

  const handleRequiredChange = (checked: boolean) => {
    onChange({
      ...field,
      required: checked
    });
  };

  const getFieldTypeLabel = () => {
    switch (field.type) {
      case 'text':
        return 'Text';
      case 'paragraph':
        return 'Paragraph';
      case 'email':
        return 'Email';
      case 'number':
        return 'Number';
      case 'date':
        return 'Date';
      case 'time':
        return 'Time';
      default:
        return field.type;
    }
  };

  return (
    <Card className="border shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start gap-2">
          <div className="text-muted-foreground pt-1.5">
            <GripVertical className="h-5 w-5 cursor-move" />
          </div>
          
          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded-md mr-2">
                    {getFieldTypeLabel()}
                  </span>
                  <Input
                    value={field.question}
                    onChange={handleQuestionChange}
                    placeholder="Enter question text"
                    className="border-none shadow-none focus-visible:ring-0 text-base font-medium p-0 h-auto"
                  />
                </div>
                
                <div className="flex items-center space-x-1">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={onMoveUp}>
                        <ArrowUp className="h-4 w-4 mr-2" />
                        Move Up
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={onMoveDown}>
                        <ArrowDown className="h-4 w-4 mr-2" />
                        Move Down
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive" 
                        onClick={onDelete}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? "Less options" : "More options"}
                  </Button>
                </div>
              </div>
              
              {isExpanded && (
                <div className="space-y-4 pt-2">
                  <div className="space-y-2">
                    <Label htmlFor={`${field.id}-description`}>Description (optional)</Label>
                    <Textarea
                      id={`${field.id}-description`}
                      value={field.description || ''}
                      onChange={handleDescriptionChange}
                      placeholder="Add a description"
                      className="resize-none"
                      rows={2}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`${field.id}-placeholder`}>Placeholder text (optional)</Label>
                    <Input
                      id={`${field.id}-placeholder`}
                      value={field.placeholder || ''}
                      onChange={handlePlaceholderChange}
                      placeholder="Add placeholder text"
                    />
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center justify-end">
              <div className="flex items-center space-x-2">
                <Label htmlFor={`${field.id}-required`} className="text-sm cursor-pointer">
                  Required
                </Label>
                <Switch
                  id={`${field.id}-required`}
                  checked={field.required}
                  onCheckedChange={handleRequiredChange}
                />
              </div>
            </div>
            
            <div className="pt-2 border-t">
              <div className="text-sm text-muted-foreground mb-2">Preview:</div>
              {field.type === 'paragraph' ? (
                <Textarea 
                  disabled 
                  placeholder={field.placeholder || 'Enter text here...'}
                  className="bg-background resize-none"
                  rows={3}
                />
              ) : (
                <Input 
                  type={field.type === 'email' ? 'email' : field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : field.type === 'time' ? 'time' : 'text'}
                  disabled 
                  placeholder={field.placeholder || 'Enter text here...'}
                  className="bg-background"
                />
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TextField;
