
import React, { useState } from 'react';
import { Survey, SurveyResponse, SurveyAnswer } from '@/types';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  Clock, 
  Mail, 
  User 
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ResponsesListProps {
  responses: SurveyResponse[];
  survey: Survey | null;
}

const ResponsesList: React.FC<ResponsesListProps> = ({ responses, survey }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedResponse, setExpandedResponse] = useState<string | null>(null);
  
  if (!survey) return null;
  
  const filteredResponses = responses.filter(response => {
    const email = response.respondent_email?.toLowerCase() || '';
    return email.includes(searchQuery.toLowerCase());
  });
  
  const toggleExpandResponse = (responseId: string) => {
    if (expandedResponse === responseId) {
      setExpandedResponse(null);
    } else {
      setExpandedResponse(responseId);
    }
  };
  
  const getFieldQuestion = (fieldId: string) => {
    for (const section of survey.sections) {
      for (const field of section.fields) {
        if (field.id === fieldId) {
          return field.question;
        }
      }
    }
    return 'Unknown Question';
  };
  
  const getFieldType = (fieldId: string) => {
    for (const section of survey.sections) {
      for (const field of section.fields) {
        if (field.id === fieldId) {
          return field.type;
        }
      }
    }
    return 'text';
  };
  
  const formatResponseValue = (answer: SurveyAnswer) => {
    const fieldType = getFieldType(answer.field_id);
    
    if (answer.value_json) {
      try {
        const jsonValue = JSON.parse(answer.value_json);
        if (Array.isArray(jsonValue)) {
          return jsonValue.join(', ');
        }
        return JSON.stringify(jsonValue);
      } catch (e) {
        return answer.value_json;
      }
    }
    
    if (fieldType === 'date' && answer.value) {
      return new Date(answer.value).toLocaleDateString();
    }
    
    return answer.value || 'No response';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      
      {filteredResponses.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">No responses found.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredResponses.map((response) => (
            <Card key={response.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center text-lg">
                      {response.respondent_email ? (
                        <Mail className="mr-2 h-4 w-4" />
                      ) : (
                        <User className="mr-2 h-4 w-4" />
                      )}
                      {response.respondent_email || 'Anonymous Respondent'}
                    </CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <Clock className="mr-1 h-3 w-3" />
                      {response.completed_at 
                        ? `Completed ${formatDistanceToNow(new Date(response.completed_at), { addSuffix: true })}` 
                        : `Started ${formatDistanceToNow(new Date(response.started_at), { addSuffix: true })}`
                      }
                    </CardDescription>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => toggleExpandResponse(response.id)}
                  >
                    {expandedResponse === response.id ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              
              {expandedResponse === response.id && (
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-1/2">Question</TableHead>
                        <TableHead>Answer</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {response.answers && response.answers.map((answer) => (
                        <TableRow key={answer.id}>
                          <TableCell className="font-medium">
                            {getFieldQuestion(answer.field_id)}
                          </TableCell>
                          <TableCell>
                            {formatResponseValue(answer)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ResponsesList;
