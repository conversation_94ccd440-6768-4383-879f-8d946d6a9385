
import React from 'react';
import { Survey, SurveyResponse, Field } from '@/types';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from '@/components/ui/chart';
import { 
  Bar<PERSON>hart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  Legend
} from 'recharts';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ResponseAnalyticsProps {
  responses: SurveyResponse[];
  survey: Survey | null;
}

const COLORS = ['#2563EB', '#4F46E5', '#7C3AED', '#9333EA', '#C026D3', '#DB2777', '#E11D48', '#F97316', '#FACC15'];

const ResponseAnalytics: React.FC<ResponseAnalyticsProps> = ({ responses, survey }) => {
  if (!survey || responses.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6 text-center">
          <p className="text-muted-foreground">No data available for analysis.</p>
        </CardContent>
      </Card>
    );
  }

  // Find multipleChoice, checkbox, and dropdown fields for analytics
  const chartableFields = survey.sections.flatMap(section => 
    section.fields.filter(field => 
      ['multipleChoice', 'checkbox', 'dropdown'].includes(field.type)
    )
  );

  const getFieldData = (field: Field) => {
    const counts: Record<string, number> = {};
    
    responses.forEach(response => {
      if (!response.answers) return;
      
      const answer = response.answers.find(a => a.field_id === field.id);
      if (!answer) return;
      
      if (field.type === 'checkbox' && answer.value_json) {
        try {
          const values = JSON.parse(answer.value_json);
          if (Array.isArray(values)) {
            values.forEach(val => {
              counts[val] = (counts[val] || 0) + 1;
            });
          }
        } catch (e) {
          console.error('Error parsing checkbox values:', e);
        }
      } else if (answer.value) {
        counts[answer.value] = (counts[answer.value] || 0) + 1;
      }
    });
    
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  };

  const getCompletionRate = () => {
    const completed = responses.filter(r => r.completed_at).length;
    return [
      { name: 'Completed', value: completed },
      { name: 'Incomplete', value: responses.length - completed }
    ];
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Response Summary</CardTitle>
            <CardDescription>Summary of survey responses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={getCompletionRate()}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {getCompletionRate().map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Responses Over Time</CardTitle>
            <CardDescription>Number of responses received over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={[
                  { name: 'Total', value: responses.length },
                  { name: 'Today', value: responses.filter(r => 
                    new Date(r.completed_at || r.started_at).toDateString() === new Date().toDateString()
                  ).length }
                ]}>
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#2563EB" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {chartableFields.length > 0 ? (
        <Tabs defaultValue={chartableFields[0].id}>
          <TabsList className="mb-6">
            {chartableFields.map(field => (
              <TabsTrigger key={field.id} value={field.id}>
                {field.question}
              </TabsTrigger>
            ))}
          </TabsList>

          {chartableFields.map(field => (
            <TabsContent key={field.id} value={field.id}>
              <Card>
                <CardHeader>
                  <CardTitle>{field.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      {field.type === 'checkbox' ? (
                        <BarChart data={getFieldData(field)}>
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="value" fill="#2563EB" />
                        </BarChart>
                      ) : (
                        <PieChart>
                          <Pie
                            data={getFieldData(field)}
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {getFieldData(field).map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      )}
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">No multiple choice or checkbox questions found for visualization.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ResponseAnalytics;
