# Survey Online - Project Rules and Patterns

## Code Organization

### Component Structure
- Use functional components with TypeScript interfaces for props
- Organize complex components into logical sections with clear comments
- Keep UI components small and focused on a single responsibility
- Extract reusable UI elements to the components/ui directory

### State Management
- Use React Context for global state (SurveyContext)
- Use local component state for UI-specific state
- Prefer useReducer for complex state management within components

## Naming Conventions

### Files and Components
- PascalCase for component files and names (e.g., SurveyCreator.tsx)
- camelCase for utility files
- Use descriptive, action-oriented names for handler functions (e.g., handleSaveSurvey)

### CSS Classes
- Use Tailwind CSS utility classes for styling
- Follow BEM-like naming for custom classes when needed

## Type Definitions
- Define interfaces for all component props
- Use TypeScript for type safety throughout the application
- Place shared types in the types directory

## UI Patterns
- Use shadcn/ui components for consistent UI
- Follow mobile-first responsive design principles
- Use Lucide icons for consistent iconography
- Implement toast notifications for user feedback

## Testing Approach
- Write unit tests for utility functions
- Component tests should focus on user interactions
- Test form validation logic thoroughly

## Commit Guidelines
- Use conventional commits format
- Reference task IDs in commit messages when applicable
- Keep commits focused on single changes or features

## Performance Considerations
- Optimize renders with useMemo and useCallback for expensive computations
- Use virtualization for long lists (fields, sections, surveys)
- Implement pagination for API requests with large datasets 