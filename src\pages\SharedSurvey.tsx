import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import { Field, Survey, FieldType } from '@/types';
import { loadPublicSurvey, createSurveyResponse, submitSurveyAnswers } from '@/services/surveyResponseService';
import { Star } from 'lucide-react';

const SharedSurvey: React.FC = () => {
  const { shareLink } = useParams();
  const navigate = useNavigate();
  const [survey, setSurvey] = useState<Survey | null>(null);
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [formResponses, setFormResponses] = useState<Record<string, any>>({});
  const [responseId, setResponseId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSurvey = async () => {
      if (!shareLink) return;
      
      try {
        const surveyData = await loadPublicSurvey(shareLink);
        if (!surveyData) {
          setError('Survey not found or no longer available');
          return;
        }
        
        setSurvey(surveyData);
        
        // Create a new response record with metadata
        const response = await createSurveyResponse(
          surveyData.id, 
          undefined, // No email for anonymous responses
          { share_link: shareLink } // Pass metadata as third parameter
        );
        
        setResponseId(response.id);
      } catch (error) {
        console.error('Error loading survey:', error);
        setError('Failed to load survey');
      } finally {
        setIsLoading(false);
      }
    };

    loadSurvey();
  }, [shareLink]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !survey) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>{error || 'Survey not found'}</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const sections = survey.sections;
  const currentSection = sections[currentSectionIndex];

  const handlePrevious = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(currentSectionIndex - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleNext = async () => {
    const requiredFields = currentSection.fields.filter(field => field.required);
    const missingFields = requiredFields.filter(field => !formResponses[field.id]);
    
    if (missingFields.length > 0) {
      toast.error('Please answer all required questions');
      return;
    }
    
    if (currentSectionIndex < sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
      window.scrollTo(0, 0);
    } else {
      try {
        // Submit all answers with the correct format
        const answers = Object.entries(formResponses).map(([fieldId, value]) => ({
          fieldId, // Use camelCase to match the expected parameter type
          value: value
        }));

        await submitSurveyAnswers(responseId!, answers);
        
        toast.success('Thank you for completing the survey!');
        setTimeout(() => {
          navigate('/survey-completed');
        }, 2000);
      } catch (error) {
        console.error('Error submitting survey:', error);
        toast.error('Failed to submit survey. Please try again.');
      }
    }
  };

  const updateResponse = (fieldId: string, value: any) => {
    setFormResponses({
      ...formResponses,
      [fieldId]: value
    });
  };

  const renderField = (field: Field) => {
    switch (field.type) {
      case 'text':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <Input
              id={field.id}
              placeholder={field.placeholder}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
            />
          </div>
        );
        
      case 'email':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <Input
              id={field.id}
              type="email"
              placeholder={field.placeholder || '<EMAIL>'}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
            />
          </div>
        );
        
      case 'paragraph':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.id} className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <Textarea
              id={field.id}
              placeholder={field.placeholder}
              value={formResponses[field.id] || ''}
              onChange={(e) => updateResponse(field.id, e.target.value)}
              className="w-full"
              rows={4}
            />
          </div>
        );
        
      case 'multipleChoice':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <RadioGroup
              value={formResponses[field.id] || ''}
              onValueChange={(value) => updateResponse(field.id, value)}
              className="space-y-2"
            >
              {field.options?.map((option, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`${field.id}-option-${i}`} />
                  <Label htmlFor={`${field.id}-option-${i}`} className="cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        );
        
      case 'checkbox':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <div className="space-y-2">
              {field.options?.map((option, i) => {
                const checkboxValues = formResponses[field.id] || [];
                const isChecked = checkboxValues.includes(option);
                
                return (
                  <div key={i} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${field.id}-option-${i}`}
                      checked={isChecked}
                      onCheckedChange={(checked) => {
                        const currentValues = formResponses[field.id] || [];
                        let newValues;
                        
                        if (checked) {
                          newValues = [...currentValues, option];
                        } else {
                          newValues = currentValues.filter((val: string) => val !== option);
                        }
                        
                        updateResponse(field.id, newValues);
                      }}
                    />
                    <Label htmlFor={`${field.id}-option-${i}`} className="cursor-pointer">
                      {option}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>
        );
        
      case 'rating':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                {Array.from({ length: field.max || 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`h-6 w-6 cursor-pointer ${
                      i < (formResponses[field.id] || 0)
                        ? 'text-yellow-400'
                        : 'text-muted-foreground/30'
                    }`}
                    onClick={() => updateResponse(field.id, i + 1)}
                  />
                ))}
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                {field.minLabel && <span>{field.minLabel}</span>}
                <span>to</span>
                {field.maxLabel && <span>{field.maxLabel}</span>}
              </div>
            </div>
          </div>
        );

      case 'multipleRating':
        return (
          <div className="space-y-3">
            <Label className="flex items-center">
              {field.question}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {field.description && (
              <p className="text-sm text-muted-foreground">{field.description}</p>
            )}
            <div className="space-y-4">
              {field.options?.map((option, i) => {
                const currentRating = formResponses[field.id]?.[option] || 0;
                return (
                  <div key={i} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{option}</span>
                    <div className="flex items-center space-x-2">
                      {field.minLabel && <span className="text-xs text-muted-foreground">{field.minLabel}</span>}
                      <div className="flex items-center">
                        {Array.from({ length: field.max || 5 }, (_, starIndex) => (
                          <Star
                            key={starIndex}
                            className={`h-5 w-5 cursor-pointer ${
                              starIndex < currentRating
                                ? 'text-yellow-400 fill-current'
                                : 'text-muted-foreground/30'
                            }`}
                            onClick={() => {
                              const currentResponses = formResponses[field.id] || {};
                              updateResponse(field.id, {
                                ...currentResponses,
                                [option]: starIndex + 1
                              });
                            }}
                          />
                        ))}
                      </div>
                      {field.maxLabel && <span className="text-xs text-muted-foreground">{field.maxLabel}</span>}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-muted p-4 rounded-md">
            <p>Field type {field.type} not supported</p>
          </div>
        );
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{survey.title}</CardTitle>
          {survey.description && (
            <CardDescription>{survey.description}</CardDescription>
          )}
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Progress indicator */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Section {currentSectionIndex + 1} of {sections.length}</span>
            <span>{Math.round(((currentSectionIndex + 1) / sections.length) * 100)}% Complete</span>
          </div>
          
          {/* Current section */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">
                {currentSection.title}
              </h3>
              {currentSection.description && (
                <p className="text-sm text-muted-foreground mb-4">
                  {currentSection.description}
                </p>
              )}
            </div>
            
            {/* Fields */}
            <div className="space-y-6">
              {currentSection.fields.map((field) => (
                <div key={field.id}>
                  {renderField(field)}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentSectionIndex === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          
          <Button onClick={handleNext}>
            {currentSectionIndex === sections.length - 1 ? 'Submit' : 'Next'}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SharedSurvey;
