// import React from 'react';
// import { useNavigate } from 'react-router-dom';
// import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import { ClipboardList, Calendar, FileText } from 'lucide-react';
// import { useSurvey } from '@/context/SurveyContext';
// import { generateSurveyTemplate, TemplateType } from '@/services/templateService';
// import { toast } from 'sonner';

// const TemplateSelector: React.FC = () => {
//   const navigate = useNavigate();
//   const { createSurvey, updateSurvey, isLoading } = useSurvey();

//   const templates = [
//     {
//       id: 'customerFeedback',
//       title: 'Customer Feedback',
//       description: 'Collect feedback about your products or services from customers',
//       icon: <ClipboardList className="h-8 w-8 text-primary" />,
//     },
//     {
//       id: 'eventRegistration',
//       title: 'Event Registration',
//       description: 'Register participants for your upcoming event',
//       icon: <Calendar className="h-8 w-8 text-primary" />,
//     },
//     {
//       id: 'researchStudy',
//       title: 'Research Study',
//       description: 'Gather data for academic or market research',
//       icon: <FileText className="h-8 w-8 text-primary" />,
//     },
//   ];

//   const handleTemplateSelect = async (templateType: TemplateType) => {
//     try {
//       // Create an empty survey first to get an ID
//       const emptySurvey = await createSurvey('New Survey', '');
      
//       // Generate template with the survey ID
//       const template = generateSurveyTemplate(templateType, emptySurvey.id);
      
//       // Update the survey with the template content
//       await updateSurvey({
//         ...emptySurvey,
//         title: template.title,
//         description: template.description,
//         sections: template.sections,
//       });
      
//       toast.success('Survey template created successfully!');
      
//       // Navigate to the survey editor
//       navigate(`/create/${emptySurvey.id}`);
//     } catch (error) {
//       console.error('Error creating template:', error);
//       toast.error('Failed to create survey template');
//     }
//   };

//   return (
//     <div className="container mx-auto py-8">
//       <h1 className="text-2xl font-bold mb-6">Select a Survey Template</h1>
      
//       <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//         {templates.map((template) => (
//           <Card key={template.id} className="hover:shadow-md transition-shadow">
//             <CardHeader>
//               <div className="flex justify-center mb-4">
//                 {template.icon}
//               </div>
//               <CardTitle className="text-xl text-center">{template.title}</CardTitle>
//               <CardDescription className="text-center">{template.description}</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <p className="text-sm text-muted-foreground">
//                 This template provides a pre-built survey structure for {template.title.toLowerCase()} purposes.
//               </p>
//             </CardContent>
//             <CardFooter className="flex justify-center">
//               <Button 
//                 onClick={() => handleTemplateSelect(template.id as TemplateType)} 
//                 disabled={isLoading}
//               >
//                 Use Template
//               </Button>
//             </CardFooter>
//           </Card>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default TemplateSelector; 