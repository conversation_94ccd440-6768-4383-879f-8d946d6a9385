import React, { createContext, useContext, useState, useEffect } from 'react';
import { Survey, Section, Field, FieldType } from '../types';
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from '@/context/AuthContext';

type SurveyContextType = {
  surveys: Survey[];
  currentSurvey: Survey | null;
  setCurrentSurvey: (survey: Survey) => void;
  createSurvey: (title: string, description?: string) => Promise<Survey>;
  updateSurvey: (survey: Survey) => Promise<void>;
  deleteSurvey: (id: string) => Promise<void>;
  publishSurvey: (id: string) => Promise<void>;
  unpublishSurvey: (id: string) => Promise<void>;
  addSection: (surveyId: string, title: string, description?: string) => Promise<void>;
  updateSection: (surveyId: string, section: Section) => Promise<void>;
  deleteSection: (surveyId: string, sectionId: string) => Promise<void>;
  addField: (surveyId: string, sectionId: string, type: FieldType, question: string) => Promise<void>;
  updateField: (surveyId: string, sectionId: string, field: Field) => Promise<void>;
  deleteField: (surveyId: string, sectionId: string, fieldId: string) => Promise<void>;
  moveSectionUp: (surveyId: string, sectionId: string) => Promise<void>;
  moveSectionDown: (surveyId: string, sectionId: string) => Promise<void>;
  moveFieldUp: (surveyId: string, sectionId: string, fieldId: string) => Promise<void>;
  moveFieldDown: (surveyId: string, sectionId: string, fieldId: string) => Promise<void>;
  fetchSurveyById: (id: string) => Promise<Survey | null>;
  fetchSurveysByUser: () => Promise<void>;
  isLoading: boolean;
  generateShareLink: (id: string) => Promise<void>;
};

const SurveyContext = createContext<SurveyContextType | undefined>(undefined);

export const SurveyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [surveys, setSurveys] = useState<Survey[]>([]);
  const [currentSurvey, setCurrentSurvey] = useState<Survey | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchSurveysByUser();
    }
  }, [user]);

  const fetchSurveysByUser = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      const { data: surveyData, error: surveyError } = await supabase
        .from('sv_surveys')
        .select('*')
        .order('updated_at', { ascending: false });
      
      if (surveyError) throw surveyError;
      
      const surveysWithData = await Promise.all(surveyData.map(async (survey) => {
        const { data: sectionData, error: sectionError } = await supabase
          .from('sv_sections')
          .select('*')
          .eq('survey_id', survey.id)
          .order('order_index', { ascending: true });
        
        if (sectionError) throw sectionError;
        
        const sectionsWithFields = await Promise.all(sectionData.map(async (section) => {
          const { data: fieldData, error: fieldError } = await supabase
            .from('sv_fields')
            .select('*')
            .eq('section_id', section.id)
            .order('order_index', { ascending: true });
          
          if (fieldError) throw fieldError;
          
          const processedFields = fieldData.map(field => ({
            ...field,
            options: field.options ? JSON.parse(JSON.stringify(field.options)) : undefined,
            type: field.type as FieldType
          }));
          
          return {
            ...section,
            fields: processedFields
          };
        }));
        
        const { count: responseCount, error: countError } = await supabase
          .from('sv_responses')
          .select('*', { count: 'exact', head: true })
          .eq('survey_id', survey.id);
          
        if (countError) throw countError;
        
        return {
          ...survey,
          sections: sectionsWithFields,
          responses: responseCount || 0
        };
      }));
      
      setSurveys(surveysWithData);
    } catch (error) {
      console.error('Error fetching surveys:', error);
      toast.error("Failed to load your surveys");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSurveyById = async (id: string): Promise<Survey | null> => {
    setIsLoading(true);
    try {
      const { data: surveyData, error: surveyError } = await supabase
        .from('sv_surveys')
        .select('*')
        .eq('id', id)
        .single();
      
      if (surveyError) throw surveyError;
      if (!surveyData) return null;
      
      const { data: sectionData, error: sectionError } = await supabase
        .from('sv_sections')
        .select('*')
        .eq('survey_id', id)
        .order('order_index', { ascending: true });
      
      if (sectionError) throw sectionError;
      
      const sectionsWithFields = await Promise.all(sectionData.map(async (section) => {
        const { data: fieldData, error: fieldError } = await supabase
          .from('sv_fields')
          .select('*')
          .eq('section_id', section.id)
          .order('order_index', { ascending: true });
        
        if (fieldError) throw fieldError;
        
        const processedFields = fieldData.map(field => ({
          ...field,
          options: field.options ? JSON.parse(JSON.stringify(field.options)) : undefined,
          type: field.type as FieldType
        }));
        
        return {
          ...section,
          fields: processedFields
        };
      }));
      
      const { count: responseCount, error: countError } = await supabase
        .from('sv_responses')
        .select('*', { count: 'exact', head: true })
        .eq('survey_id', id);
        
      if (countError) throw countError;
      
      const survey = {
        ...surveyData,
        sections: sectionsWithFields,
        responses: responseCount || 0
      };
      
      return survey;
    } catch (error) {
      console.error('Error fetching survey:', error);
      toast.error("Failed to load survey");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const createSurvey = async (title: string, description?: string): Promise<Survey> => {
    if (!user) {
      toast.error("You must be logged in to create a survey");
      throw new Error("User not authenticated");
    }
    
    setIsLoading(true);
    try {
      const { data: newSurvey, error } = await supabase
        .from('sv_surveys')
        .insert([{ 
          title, 
          description, 
          user_id: user.id 
        }])
        .select()
        .single();
      
      if (error) throw error;
      
      const surveyWithSections = {
        ...newSurvey,
        sections: [],
        responses: 0
      };
      
      setSurveys([surveyWithSections, ...surveys]);
      setCurrentSurvey(surveyWithSections);
      toast.success("Survey created successfully");
      return surveyWithSections;
    } catch (error) {
      console.error('Error creating survey:', error);
      toast.error("Failed to create survey");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateSurvey = async (updatedSurvey: Survey): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_surveys')
        .update({
          title: updatedSurvey.title,
          description: updatedSurvey.description,
        })
        .eq('id', updatedSurvey.id);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey =>
          survey.id === updatedSurvey.id ? updatedSurvey : survey
        )
      );
      
      if (currentSurvey?.id === updatedSurvey.id) {
        setCurrentSurvey(updatedSurvey);
      }
      
      toast.success("Survey updated successfully");
    } catch (error) {
      console.error('Error updating survey:', error);
      toast.error("Failed to update survey");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteSurvey = async (id: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_surveys')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      setSurveys(prev => prev.filter(survey => survey.id !== id));
      if (currentSurvey?.id === id) {
        setCurrentSurvey(null);
      }
      
      toast.success("Survey deleted successfully");
    } catch (error) {
      console.error('Error deleting survey:', error);
      toast.error("Failed to delete survey");
    } finally {
      setIsLoading(false);
    }
  };

  const publishSurvey = async (id: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_surveys')
        .update({ published: true })
        .eq('id', id);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey =>
          survey.id === id
            ? { ...survey, published: true }
            : survey
        )
      );
      
      if (currentSurvey?.id === id) {
        setCurrentSurvey({
          ...currentSurvey,
          published: true
        });
      }
      
      toast.success("Survey published successfully");
    } catch (error) {
      console.error('Error publishing survey:', error);
      toast.error("Failed to publish survey");
    } finally {
      setIsLoading(false);
    }
  };

  const unpublishSurvey = async (id: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_surveys')
        .update({ published: false })
        .eq('id', id);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey =>
          survey.id === id
            ? { ...survey, published: false }
            : survey
        )
      );
      
      if (currentSurvey?.id === id) {
        setCurrentSurvey({
          ...currentSurvey,
          published: false
        });
      }
      
      toast.success("Survey unpublished successfully");
    } catch (error) {
      console.error('Error unpublishing survey:', error);
      toast.error("Failed to unpublish survey");
    } finally {
      setIsLoading(false);
    }
  };

  const addSection = async (surveyId: string, title: string, description?: string): Promise<void> => {
    setIsLoading(true);
    try {
      let maxOrderIndex = -1;
      if (currentSurvey) {
        maxOrderIndex = currentSurvey.sections.reduce(
          (max, section) => Math.max(max, section.order_index), -1
        );
      }
      
      const { data: newSection, error } = await supabase
        .from('sv_sections')
        .insert([{
          survey_id: surveyId,
          title,
          description,
          order_index: maxOrderIndex + 1
        }])
        .select()
        .single();
      
      if (error) throw error;
      
      const sectionWithFields = {
        ...newSection,
        fields: []
      };
      
      setSurveys(prev =>
        prev.map(survey => {
          if (survey.id === surveyId) {
            return {
              ...survey,
              sections: [...survey.sections, sectionWithFields]
            };
          }
          return survey;
        })
      );
      
      if (currentSurvey?.id === surveyId) {
        setCurrentSurvey({
          ...currentSurvey,
          sections: [...currentSurvey.sections, sectionWithFields]
        });
      }
      
      toast.success("Section added successfully");
    } catch (error) {
      console.error('Error adding section:', error);
      toast.error("Failed to add section");
    } finally {
      setIsLoading(false);
    }
  };

  const updateSection = async (surveyId: string, updatedSection: Section): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_sections')
        .update({
          title: updatedSection.title,
          description: updatedSection.description
        })
        .eq('id', updatedSection.id);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey => {
          if (survey.id === surveyId) {
            return {
              ...survey,
              sections: survey.sections.map(section =>
                section.id === updatedSection.id ? updatedSection : section
              )
            };
          }
          return survey;
        })
      );
      
      if (currentSurvey?.id === surveyId) {
        setCurrentSurvey({
          ...currentSurvey,
          sections: currentSurvey.sections.map(section =>
            section.id === updatedSection.id ? updatedSection : section
          )
        });
      }
      
      toast.success("Section updated successfully");
    } catch (error) {
      console.error('Error updating section:', error);
      toast.error("Failed to update section");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteSection = async (surveyId: string, sectionId: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_sections')
        .delete()
        .eq('id', sectionId);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey => {
          if (survey.id === surveyId) {
            return {
              ...survey,
              sections: survey.sections.filter(section => section.id !== sectionId)
            };
          }
          return survey;
        })
      );
      
      if (currentSurvey?.id === surveyId) {
        setCurrentSurvey({
          ...currentSurvey,
          sections: currentSurvey.sections.filter(section => section.id !== sectionId)
        });
      }
      
      toast.success("Section deleted successfully");
    } catch (error) {
      console.error('Error deleting section:', error);
      toast.error("Failed to delete section");
    } finally {
      setIsLoading(false);
    }
  };

  const addField = async (surveyId: string, sectionId: string, type: FieldType, question: string): Promise<void> => {
    setIsLoading(true);
    try {
      const section = currentSurvey?.sections.find(s => s.id === sectionId);
      if (!section) throw new Error("Section not found");
      
      const maxOrderIndex = section.fields.reduce(
        (max, field) => Math.max(max, field.order_index), -1
      );
      
      let options = undefined;
      if (['multipleChoice', 'checkbox', 'dropdown'].includes(type)) {
        options = ['Option 1', 'Option 2', 'Option 3'];
      }
      
      const { data: newField, error } = await supabase
        .from('sv_fields')
        .insert([{
          section_id: sectionId,
          type,
          question,
          required: false,
          options: options ,
          order_index: maxOrderIndex + 1
        }])
        .select()
        .single();
      
      if (error) throw error;
      
      const fieldWithParsedOptions = {
        ...newField,
        options: newField.options ? JSON.parse(JSON.stringify(newField.options)) : undefined,
        type: newField.type as FieldType
      };
      
      setSurveys(prev =>
        prev.map(survey => {
          if (survey.id === surveyId) {
            return {
              ...survey,
              sections: survey.sections.map(section => {
                if (section.id === sectionId) {
                  return {
                    ...section,
                    fields: [...section.fields, fieldWithParsedOptions]
                  };
                }
                return section;
              })
            };
          }
          return survey;
        })
      );
      
      if (currentSurvey?.id === surveyId) {
        setCurrentSurvey({
          ...currentSurvey,
          sections: currentSurvey.sections.map(section => {
            if (section.id === sectionId) {
              return {
                ...section,
                fields: [...section.fields, fieldWithParsedOptions]
              };
            }
            return section;
          })
        });
      }
      
      toast.success("Field added successfully");
    } catch (error) {
      console.error('Error adding field:', error);
      toast.error("Failed to add field");
    } finally {
      setIsLoading(false);
    }
  };

  const updateField = async (surveyId: string, sectionId: string, updatedField: Field): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_fields')
        .update({
          type: updatedField.type,
          question: updatedField.question,
          description: updatedField.description,
          required: updatedField.required,
          options: updatedField.options,
          placeholder: updatedField.placeholder,
          min: updatedField.min,
          max: updatedField.max
        })
        .eq('id', updatedField.id);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey => {
          if (survey.id === surveyId) {
            return {
              ...survey,
              sections: survey.sections.map(section => {
                if (section.id === sectionId) {
                  return {
                    ...section,
                    fields: section.fields.map(field =>
                      field.id === updatedField.id ? updatedField : field
                    )
                  };
                }
                return section;
              })
            };
          }
          return survey;
        })
      );
      
      if (currentSurvey?.id === surveyId) {
        setCurrentSurvey({
          ...currentSurvey,
          sections: currentSurvey.sections.map(section => {
            if (section.id === sectionId) {
              return {
                ...section,
                fields: section.fields.map(field =>
                  field.id === updatedField.id ? updatedField : field
                )
              };
            }
            return section;
          })
        });
      }
      
      toast.success("Field updated successfully");
    } catch (error) {
      console.error('Error updating field:', error);
      toast.error("Failed to update field");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteField = async (surveyId: string, sectionId: string, fieldId: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('sv_fields')
        .delete()
        .eq('id', fieldId);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey => {
          if (survey.id === surveyId) {
            return {
              ...survey,
              sections: survey.sections.map(section => {
                if (section.id === sectionId) {
                  return {
                    ...section,
                    fields: section.fields.filter(field => field.id !== fieldId)
                  };
                }
                return section;
              })
            };
          }
          return survey;
        })
      );
      
      if (currentSurvey?.id === surveyId) {
        setCurrentSurvey({
          ...currentSurvey,
          sections: currentSurvey.sections.map(section => {
            if (section.id === sectionId) {
              return {
                ...section,
                fields: section.fields.filter(field => field.id !== fieldId)
              };
            }
            return section;
          })
        });
      }
      
      toast.success("Field deleted successfully");
    } catch (error) {
      console.error('Error deleting field:', error);
      toast.error("Failed to delete field");
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderIndices = async (
    table: 'sv_sections' | 'sv_fields',
    items: { id: string; order_index: number }[]
  ) => {
    const updates = items.map(item => ({
      id: item.id,
      order_index: item.order_index
    }));
    
    for (const update of updates) {
      const { error } = await supabase
        .from(table)
        .update({ order_index: update.order_index })
        .eq('id', update.id);
      
      if (error) throw error;
    }
  };

  const moveSectionUp = async (surveyId: string, sectionId: string): Promise<void> => {
    if (!currentSurvey) return;
    
    const sectionIndex = currentSurvey.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex <= 0) return;
    
    try {
      setIsLoading(true);
      
      const newSections = [...currentSurvey.sections];
      const temp = newSections[sectionIndex].order_index;
      newSections[sectionIndex].order_index = newSections[sectionIndex - 1].order_index;
      newSections[sectionIndex - 1].order_index = temp;
      
      await updateOrderIndices('sv_sections', [
        newSections[sectionIndex],
        newSections[sectionIndex - 1]
      ]);
      
      newSections.sort((a, b) => a.order_index - b.order_index);
      
      const updatedSurvey = {
        ...currentSurvey,
        sections: newSections
      };
      
      setSurveys(prev =>
        prev.map(survey => survey.id === surveyId ? updatedSurvey : survey)
      );
      
      setCurrentSurvey(updatedSurvey);
    } catch (error) {
      console.error('Error moving section up:', error);
      toast.error("Failed to reorder sections");
    } finally {
      setIsLoading(false);
    }
  };

  const moveSectionDown = async (surveyId: string, sectionId: string): Promise<void> => {
    if (!currentSurvey) return;
    
    const sectionIndex = currentSurvey.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex >= currentSurvey.sections.length - 1) return;
    
    try {
      setIsLoading(true);
      
      const newSections = [...currentSurvey.sections];
      const temp = newSections[sectionIndex].order_index;
      newSections[sectionIndex].order_index = newSections[sectionIndex + 1].order_index;
      newSections[sectionIndex + 1].order_index = temp;
      
      await updateOrderIndices('sv_sections', [
        newSections[sectionIndex],
        newSections[sectionIndex + 1]
      ]);
      
      newSections.sort((a, b) => a.order_index - b.order_index);
      
      const updatedSurvey = {
        ...currentSurvey,
        sections: newSections
      };
      
      setSurveys(prev =>
        prev.map(survey => survey.id === surveyId ? updatedSurvey : survey)
      );
      
      setCurrentSurvey(updatedSurvey);
    } catch (error) {
      console.error('Error moving section down:', error);
      toast.error("Failed to reorder sections");
    } finally {
      setIsLoading(false);
    }
  };

  const moveFieldUp = async (surveyId: string, sectionId: string, fieldId: string): Promise<void> => {
    if (!currentSurvey) return;
    
    const section = currentSurvey.sections.find(s => s.id === sectionId);
    if (!section) return;
    
    const fieldIndex = section.fields.findIndex(f => f.id === fieldId);
    if (fieldIndex <= 0) return;
    
    try {
      setIsLoading(true);
      
      const newFields = [...section.fields];
      const temp = newFields[fieldIndex].order_index;
      newFields[fieldIndex].order_index = newFields[fieldIndex - 1].order_index;
      newFields[fieldIndex - 1].order_index = temp;
      
      await updateOrderIndices('sv_fields', [
        newFields[fieldIndex],
        newFields[fieldIndex - 1]
      ]);
      
      newFields.sort((a, b) => a.order_index - b.order_index);
      
      const updatedSections = currentSurvey.sections.map(s =>
        s.id === sectionId ? { ...s, fields: newFields } : s
      );
      
      const updatedSurvey = {
        ...currentSurvey,
        sections: updatedSections
      };
      
      setSurveys(prev =>
        prev.map(survey => survey.id === surveyId ? updatedSurvey : survey)
      );
      
      setCurrentSurvey(updatedSurvey);
    } catch (error) {
      console.error('Error moving field up:', error);
      toast.error("Failed to reorder fields");
    } finally {
      setIsLoading(false);
    }
  };

  const moveFieldDown = async (surveyId: string, sectionId: string, fieldId: string): Promise<void> => {
    if (!currentSurvey) return;
    
    const section = currentSurvey.sections.find(s => s.id === sectionId);
    if (!section) return;
    
    const fieldIndex = section.fields.findIndex(f => f.id === fieldId);
    if (fieldIndex >= section.fields.length - 1) return;
    
    try {
      setIsLoading(true);
      
      const newFields = [...section.fields];
      const temp = newFields[fieldIndex].order_index;
      newFields[fieldIndex].order_index = newFields[fieldIndex + 1].order_index;
      newFields[fieldIndex + 1].order_index = temp;
      
      await updateOrderIndices('sv_fields', [
        newFields[fieldIndex],
        newFields[fieldIndex + 1]
      ]);
      
      newFields.sort((a, b) => a.order_index - b.order_index);
      
      const updatedSections = currentSurvey.sections.map(s =>
        s.id === sectionId ? { ...s, fields: newFields } : s
      );
      
      const updatedSurvey = {
        ...currentSurvey,
        sections: updatedSections
      };
      
      setSurveys(prev =>
        prev.map(survey => survey.id === surveyId ? updatedSurvey : survey)
      );
      
      setCurrentSurvey(updatedSurvey);
    } catch (error) {
      console.error('Error moving field down:', error);
      toast.error("Failed to reorder fields");
    } finally {
      setIsLoading(false);
    }
  };

  const generateShareLink = async (id: string): Promise<void> => {
    setIsLoading(true);
    try {
      const shareLink = Math.random().toString(36).substring(2, 10);
      
      const { error } = await supabase
        .from('sv_surveys')
        .update({ share_link: shareLink })
        .eq('id', id);
      
      if (error) throw error;
      
      setSurveys(prev =>
        prev.map(survey =>
          survey.id === id
            ? { ...survey, share_link: shareLink }
            : survey
        )
      );
      
      if (currentSurvey?.id === id) {
        setCurrentSurvey({
          ...currentSurvey,
          share_link: shareLink
        });
      }
      
      toast.success("Share link generated successfully");
    } catch (error) {
      console.error('Error generating share link:', error);
      toast.error("Failed to generate share link");
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: SurveyContextType = {
    surveys,
    currentSurvey,
    setCurrentSurvey,
    createSurvey,
    updateSurvey,
    deleteSurvey,
    publishSurvey,
    unpublishSurvey,
    addSection,
    updateSection,
    deleteSection,
    addField,
    updateField,
    deleteField,
    moveSectionUp,
    moveSectionDown,
    moveFieldUp,
    moveFieldDown,
    fetchSurveyById,
    fetchSurveysByUser,
    isLoading,
    generateShareLink
  };

  return (
    <SurveyContext.Provider value={contextValue}>{children}</SurveyContext.Provider>
  );
};

export const useSurvey = () => {
  const context = useContext(SurveyContext);
  if (context === undefined) {
    throw new Error('useSurvey must be used within a SurveyProvider');
  }
  return context;
};
