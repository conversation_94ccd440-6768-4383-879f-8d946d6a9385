import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { PlusCircle, Type, AlignLeft, ListChecks, Check, Calendar, Clock, Mail, Hash, Star, FileText } from 'lucide-react';
import { FieldType } from '@/types';

interface FieldTypeSelectorProps {
  onSelectType: (type: FieldType) => void;
}

interface FieldTypeOption {
  type: FieldType;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const fieldTypes: FieldTypeOption[] = [
  {
    type: 'text',
    label: 'Short Text',
    icon: <Type className="h-5 w-5" />,
    description: 'For short answers like names, titles, etc.'
  },
  {
    type: 'paragraph',
    label: 'Paragraph',
    icon: <AlignLeft className="h-5 w-5" />,
    description: 'For longer text responses and comments'
  },
  {
    type: 'multipleChoice',
    label: 'Multiple Choice',
    icon: <ListChecks className="h-5 w-5" />,
    description: 'Single selection from a list of options'
  },
  {
    type: 'checkbox',
    label: 'Checkboxes',
    icon: <Check className="h-5 w-5" />,
    description: 'Multiple selections from a list of options'
  },
  {
    type: 'dropdown',
    label: 'Dropdown',
    icon: <ChevronIcon className="h-5 w-5" />,
    description: 'Select from a dropdown menu'
  },
  {
    type: 'date',
    label: 'Date',
    icon: <Calendar className="h-5 w-5" />,
    description: 'For collecting calendar dates'
  },
  {
    type: 'time',
    label: 'Time',
    icon: <Clock className="h-5 w-5" />,
    description: 'For collecting time information'
  },
  {
    type: 'email',
    label: 'Email',
    icon: <Mail className="h-5 w-5" />,
    description: 'For email addresses with validation'
  },
  {
    type: 'number',
    label: 'Number',
    icon: <Hash className="h-5 w-5" />,
    description: 'For numeric input'
  },
  {
    type: 'rating',
    label: 'Rating',
    icon: <Star className="h-5 w-5" />,
    description: 'For rating scales'
  },
  {
    type: 'file',
    label: 'File Upload',
    icon: <FileText className="h-5 w-5" />,
    description: 'For file attachments'
  }
];

function ChevronIcon({ className }: { className?: string }) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      className={className}
    >
      <path d="m6 9 6 6 6-6"/>
    </svg>
  );
}

const FieldTypeSelector: React.FC<FieldTypeSelectorProps> = ({ onSelectType }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelectType = (type: FieldType) => {
    onSelectType(type);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Question
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Select Field Type</DialogTitle>
          <DialogDescription>
            Choose a field type to add to your form
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 py-4">
          {fieldTypes.map((fieldType) => (
            <Button
              key={fieldType.type}
              variant="outline"
              className="flex flex-col items-start h-auto p-4 space-y-1 hover:bg-accent"
              onClick={() => handleSelectType(fieldType.type)}
            >
              <div className="flex items-center w-full">
                <div className="text-primary">{fieldType.icon}</div>
                <div className="ml-3 font-medium">{fieldType.label}</div>
              </div>
              <div className="text-xs text-muted-foreground line-clamp-2">
                {fieldType.description}
              </div>
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FieldTypeSelector;
