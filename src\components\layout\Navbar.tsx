import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle, LayoutDashboard, ChevronRight, LogOut, LogIn } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

const Navbar: React.FC = () => {
  const location = useLocation();
  const { user, signOut } = useAuth();

  // Check if we're on a shared survey page
  const isSharedSurvey = location.pathname.startsWith('/s/');

  // Don't show navbar for shared surveys
  if (isSharedSurvey) {
    return null;
  }

  const getUserInitials = () => {
    if (!user || !user.email) return '?';
    return user.email.charAt(0).toUpperCase();
  };

  return (
    <header className="w-full sticky top-0 z-50 glass border-b border-gray-100 backdrop-blur-md">
      <div className="container flex items-center justify-between h-16 px-4 mx-auto">
        <div className="flex items-center space-x-2">
          <Link to="/" className="flex items-center">
            <span className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">
              Questioneer
            </span>
          </Link>
          
          {location.pathname !== '/' && (
            <div className="hidden md:flex items-center text-sm text-muted-foreground">
              <ChevronRight className="h-4 w-4 mx-1" />
              <span>
                {location.pathname.includes('/create') && 'Create Survey'}
                {location.pathname.includes('/dashboard') && 'Dashboard'}
                {location.pathname.includes('/preview') && 'Preview'}
                {location.pathname.includes('/auth') && 'Authentication'}
              </span>
            </div>
          )}
        </div>
        
        <nav className="flex items-center space-x-4">
          {user ? (
            <>
              <Link 
                to="/dashboard" 
                className={cn(
                  "text-sm font-medium transition-colors hover:text-primary",
                  location.pathname === '/dashboard' ? "text-primary" : "text-muted-foreground"
                )}
              >
                Dashboard
              </Link>
              <Link 
                to="/create" 
                className={cn(
                  "text-sm font-medium transition-colors hover:text-primary",
                  location.pathname === '/create' ? "text-primary" : "text-muted-foreground"
                )}
              >
                Create Survey
              </Link>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>{getUserInitials()}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <Link 
              to="/auth" 
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                location.pathname === '/auth' ? "text-primary" : "text-muted-foreground"
              )}
            >
              Sign In
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Navbar;
