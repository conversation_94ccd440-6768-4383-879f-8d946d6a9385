import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useToast } from "@/components/ui/use-toast";
import { TemplateService, TemplateGoal } from "@/services/templateService";
import { useSurvey } from "@/context/SurveyContext";
import { Badge } from "@/components/ui/badge";
import { Loader2, <PERSON>rk<PERSON> } from "lucide-react";
import { motion } from "framer-motion";
import Navbar from '@/components/layout/Navbar';
import PageTransition from '@/components/layout/PageTransition';

const TemplateCategories = {
  customer_feedback: {
    title: "Customer Feedback",
    description: "Learn what your customers think about your products or services",
    icon: "💬",
    color: "bg-blue-100 text-blue-800"
  },
  event_registration: {
    title: "Event Registration",
    description: "Collect information from attendees for your upcoming event",
    icon: "📅",
    color: "bg-purple-100 text-purple-800"
  },
  research_study: {
    title: "Research Study",
    description: "Gather data for academic or market research",
    icon: "🔍",
    color: "bg-amber-100 text-amber-800"
  },
  employee_satisfaction: {
    title: "Employee Satisfaction",
    description: "Measure employee engagement and workplace satisfaction",
    icon: "👥",
    color: "bg-green-100 text-green-800"
  },
  education_assessment: {
    title: "Education Assessment",
    description: "Assess learning outcomes and gather student feedback",
    icon: "🎓",
    color: "bg-indigo-100 text-indigo-800"
  },
  product_evaluation: {
    title: "Product Evaluation",
    description: "Evaluate product features and user experience",
    icon: "📦",
    color: "bg-pink-100 text-pink-800"
  },
  conference_feedback: {
    title: "Conference Feedback",
    description: "Collect attendee feedback about a conference or event",
    icon: "🎤",
    color: "bg-teal-100 text-teal-800"
  }
};

const Templates = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { createSurvey, updateSurvey } = useSurvey();
  const [activeTemplate, setActiveTemplate] = useState<TemplateGoal>("customer_feedback");
  const [customizations, setCustomizations] = useState({
    industry: "",
    productType: "",
    eventType: "",
    researchTopic: "",
    additionalContext: ""
  });
  const [isGenerating, setIsGenerating] = useState(false);
  
  const handleGenerateTemplate = async () => {
    setIsGenerating(true);
    
    try {
      // Generate the template using the service
      const template = await TemplateService.generateTemplate(activeTemplate, customizations);
      
      // Create a new survey with basic details
      const newSurvey = await createSurvey(template.title, template.description);
      
      // Update the survey with sections and fields from the template
      await updateSurvey({
        ...newSurvey,
        sections: template.sections.map(section => ({
          ...section,
          survey_id: newSurvey.id
        }))
      });
      
      toast({
        title: "Template Generated",
        description: "Your survey has been created from the template. You can now customize it further.",
        variant: "default"
      });
      
      // Navigate to the survey editor
      navigate("/create?id=" + newSurvey.id);
    } catch (error) {
      console.error("Error generating template:", error);
      toast({
        title: "Generation Failed",
        description: "We couldn't generate your template. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };
  
  const renderCustomizationFields = () => {
    switch (activeTemplate) {
      case "customer_feedback":
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="industry">Industry</Label>
                <Select 
                  onValueChange={(value) => setCustomizations(prev => ({ ...prev, industry: value }))}
                  value={customizations.industry}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="retail">Retail</SelectItem>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="hospitality">Hospitality</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="productType">Product or Service Type</Label>
                <Input 
                  id="productType" 
                  placeholder="e.g., Mobile App, Consulting Service" 
                  value={customizations.productType}
                  onChange={(e) => setCustomizations(prev => ({ ...prev, productType: e.target.value }))}
                />
              </div>
            </div>
          </>
        );
        
      case "event_registration":
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="eventType">Event Type</Label>
                <Select 
                  onValueChange={(value) => setCustomizations(prev => ({ ...prev, eventType: value }))}
                  value={customizations.eventType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="conference">Conference</SelectItem>
                    <SelectItem value="workshop">Workshop</SelectItem>
                    <SelectItem value="webinar">Webinar</SelectItem>
                    <SelectItem value="networking">Networking Event</SelectItem>
                    <SelectItem value="corporate">Corporate Event</SelectItem>
                    <SelectItem value="social">Social Gathering</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        );
        
      case "research_study":
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="researchTopic">Research Topic</Label>
                <Input 
                  id="researchTopic" 
                  placeholder="e.g., Consumer Behavior, Market Trends" 
                  value={customizations.researchTopic}
                  onChange={(e) => setCustomizations(prev => ({ ...prev, researchTopic: e.target.value }))}
                />
              </div>
            </div>
          </>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="container mx-auto py-8"
    >
      <div className="max-w-5xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-2">Survey Templates</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Choose a template category and customize it to quickly create surveys tailored to your specific needs. Our AI will generate a perfect starting point.
          </p>
        </div>
        
        <div className="grid md:grid-cols-12 gap-6">
          <div className="md:col-span-4 space-y-4">
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-medium mb-4">Choose a template</h3>
              
              <div className="space-y-2">
                {Object.entries(TemplateCategories).map(([key, category]) => (
                  <div 
                    key={key}
                    className={`p-3 rounded-md cursor-pointer transition-colors ${
                      activeTemplate === key 
                        ? 'bg-primary/10 border border-primary/30' 
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => setActiveTemplate(key as TemplateGoal)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 flex items-center justify-center rounded-full text-lg ${category.color}`}>
                        {category.icon}
                      </div>
                      <div>
                        <h4 className="font-medium">{category.title}</h4>
                        <p className="text-xs text-muted-foreground">{category.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="md:col-span-8">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{TemplateCategories[activeTemplate]?.title || "Template"}</CardTitle>
                    <CardDescription>{TemplateCategories[activeTemplate]?.description}</CardDescription>
                  </div>
                  <Badge className="bg-primary/20 text-primary hover:bg-primary/30 gap-1">
                    <Sparkles className="w-3 h-3" />
                    AI-Powered
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <Accordion type="single" collapsible defaultValue="customization">
                    <AccordionItem value="customization">
                      <AccordionTrigger>Template Customization</AccordionTrigger>
                      <AccordionContent>
                        {renderCustomizationFields()}
                        
                        <div className="space-y-2 mt-4">
                          <Label htmlFor="additionalContext">Additional Context</Label>
                          <Textarea 
                            id="additionalContext" 
                            placeholder="Provide any additional details to customize your template further..."
                            value={customizations.additionalContext}
                            onChange={(e) => setCustomizations(prev => ({ ...prev, additionalContext: e.target.value }))}
                            className="min-h-[100px]"
                          />
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full" 
                  onClick={handleGenerateTemplate}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Template...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate AI Template
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Templates;
