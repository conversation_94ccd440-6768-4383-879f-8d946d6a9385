
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Field } from '@/types';
import { Settings, Trash2, ArrowUp, ArrowDown, PlusCircle, GripVertical, X } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MultipleChoiceFieldProps {
  field: Field;
  onChange: (updatedField: Field) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

const MultipleChoiceField: React.FC<MultipleChoiceFieldProps> = ({ 
  field, 
  onChange, 
  onDelete, 
  onMoveUp, 
  onMoveDown 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [newOption, setNewOption] = useState('');
  
  
  const raw = field.options;

// Turn it into a real array (or [] if missing / bad JSON)
const options: string[] = (() => {
  if (!raw) return [];          // nothing there
  if (Array.isArray(raw)) return raw;      // it’s already an array
  try {
    return JSON.parse(raw) as string[];    // parse the JSON string
  } catch {
    console.warn("Invalid JSON in field.options:", raw);
    return [];
  }
})();
console.log(raw,options);

  const handleQuestionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...field,
      question: e.target.value
    });
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange({
      ...field,
      description: e.target.value
    });
  };

  const handleRequiredChange = (checked: boolean) => {
    onChange({
      ...field,
      required: checked
    });
  };

  const handleOptionChange = (index: number, value: string) => {
    if (!field.options) return;
    
    const newOptions = [...field.options];
    newOptions[index] = value;
    
    onChange({
      ...field,
      options: newOptions
    });
  };

  const handleAddOption = () => {
    if (!newOption.trim()) return;
    
    onChange({
      ...field,
      options: [...(field.options || []), newOption]
    });
    
    setNewOption('');
  };

  const handleRemoveOption = (index: number) => {
    if (!field.options) return;
    
    const newOptions = [...field.options];
    newOptions.splice(index, 1);
    
    onChange({
      ...field,
      options: newOptions
    });
  };

  return (
    <Card className="border shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start gap-2">
          <div className="text-muted-foreground pt-1.5">
            <GripVertical className="h-5 w-5 cursor-move" />
          </div>
          
          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded-md mr-2">
                    Multiple Choice
                  </span>
                  <Input
                    value={field.question}
                    onChange={handleQuestionChange}
                    placeholder="Enter question text"
                    className="border-none shadow-none focus-visible:ring-0 text-base font-medium p-0 h-auto"
                  />
                </div>
                
                <div className="flex items-center space-x-1">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={onMoveUp}>
                        <ArrowUp className="h-4 w-4 mr-2" />
                        Move Up
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={onMoveDown}>
                        <ArrowDown className="h-4 w-4 mr-2" />
                        Move Down
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive" 
                        onClick={onDelete}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? "Less options" : "More options"}
                  </Button>
                </div>
              </div>
              
              {isExpanded && (
                <div className="space-y-4 pt-2">
                  <div className="space-y-2">
                    <Label htmlFor={`${field.id}-description`}>Description (optional)</Label>
                    <Textarea
                      id={`${field.id}-description`}
                      value={field.description || ''}
                      onChange={handleDescriptionChange}
                      placeholder="Add a description"
                      className="resize-none"
                      rows={2}
                    />
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="text-sm font-medium">Options</div>
                {options?.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <RadioGroup value="" disabled>
                      <RadioGroupItem value={`option-${index}`} />
                    </RadioGroup>
                    <Input
                      value={option}
                      onChange={(e) => handleOptionChange(index, e.target.value)}
                      placeholder="Option text"
                      className="flex-1"
                    />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => handleRemoveOption(index)}
                      disabled={field.options?.length === 1}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <div className="flex items-center space-x-2">
                  <RadioGroup value="" disabled>
                    <RadioGroupItem value="new-option" />
                  </RadioGroup>
                  <Input
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    placeholder="Add option"
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newOption.trim()) {
                        e.preventDefault();
                        handleAddOption();
                      }
                    }}
                  />
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleAddOption}
                    disabled={!newOption.trim()}
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-end">
              <div className="flex items-center space-x-2">
                <Label htmlFor={`${field.id}-required`} className="text-sm cursor-pointer">
                  Required
                </Label>
                <Switch
                  id={`${field.id}-required`}
                  checked={field.required}
                  onCheckedChange={handleRequiredChange}
                />
              </div>
            </div>
            
            <div className="pt-2 border-t">
              <div className="text-sm text-muted-foreground mb-2">Preview:</div>
              <RadioGroup defaultValue={field.options?.[0] || ""} disabled className="space-y-2">
                {options?.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <RadioGroupItem value={option} id={`preview-${field.id}-${index}`} />
                    <Label htmlFor={`preview-${field.id}-${index}`}>{option}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MultipleChoiceField;
