import { supabase } from "@/integrations/supabase/client";
import { Survey, Section, Field, FieldType } from "../types";
import { AIService } from "./AIService";

const TEMPLATE_CATEGORIES = [
  "customer_feedback",
  "event_registration",
  "employee_satisfaction",
  "market_research",
  "education_assessment",
  "product_evaluation",
  "conference_feedback"
];

export type TemplateGoal = 
  | "customer_feedback" 
  | "event_registration" 
  | "research_study"
  | "employee_satisfaction"
  | "education_assessment"
  | "product_evaluation"
  | "conference_feedback";

interface TemplateOptions {
  industry?: string;
  productType?: string;
  eventType?: string;
  researchTopic?: string;
  additionalContext?: string;
}

export const TemplateService = {
  async generateTemplate(goal: TemplateGoal, options?: TemplateOptions): Promise<Survey> {
    try {
      // First check if we have a cached template in Supabase
      const { data: cachedTemplate } = await supabase
        .from('sv_templates')
        .select('*')
        .eq('goal', goal)
        .eq('options', JSON.stringify(options || {}))
        .maybeSingle();
      
      if (cachedTemplate) {
        return JSON.parse(cachedTemplate.template_data);
      }
      
      // If no cached template, generate using AI
      const template = await AIService.generateSurveyTemplate({ 
        goal, 
        ...options 
      });
      
      // Cache the result for future use - convert options to JSON compatible format
      const optionsJson = options ? JSON.parse(JSON.stringify(options)) : {};
      
      await supabase
        .from('sv_templates')
        .insert({
          goal,
          options: optionsJson,
          template_data: JSON.stringify(template)
        });
      
      return template;
    } catch (error) {
      console.error('Error generating template:', error);
      
      // Fallback to hard-coded templates if AI fails
      const fallbackTemplate = generateFallbackTemplate(goal, options);
      return fallbackTemplate;
    }
  },
  
  async listTemplateCategories(): Promise<string[]> {
    return TEMPLATE_CATEGORIES;
  },
  
  async listTemplates(): Promise<{ id: string; goal: TemplateGoal; title: string; description: string }[]> {
    try {
      const { data, error } = await supabase
        .from('sv_template_categories')
        .select('*')
        .order('display_order', { ascending: true });
        
      if (error) throw error;
      
      // Type cast the goal property to TemplateGoal
      return data.map(item => ({
        id: item.id,
        goal: item.goal as TemplateGoal,
        title: item.title,
        description: item.description
      }));
    } catch (error) {
      console.error('Error listing templates:', error);
      return [];
    }
  }
};

// Fallback template generator if AI fails
function generateFallbackTemplate(goal: TemplateGoal, options?: TemplateOptions): Survey {
  // Here you would integrate with OpenAI or another AI provider
  // For now we'll return mock data based on the goal
  
  const baseTemplate: Partial<Survey> = {
    title: getDefaultTitle(goal),
    description: getDefaultDescription(goal),
    published: false,
    sections: []
  };
  
  // Add relevant sections and fields based on the goal
  switch (goal) {
    case "customer_feedback":
      baseTemplate.sections = [
        createSection("Product Experience", "Tell us about your experience with our product", [
          createField("multipleChoice", "How satisfied are you with our product?", true, ["Very Satisfied", "Satisfied", "Neutral", "Dissatisfied", "Very Dissatisfied"]),
          createField("paragraph", "What do you like most about our product?"),
          createField("paragraph", "How could we improve our product?")
        ]),
        createSection("Customer Service", "Rate your experience with our customer service team", [
          createField("rating", "How would you rate our customer service?", true),
          createField("paragraph", "Is there anything else you'd like to share about your customer service experience?")
        ])
      ];
      break;
      
    case "event_registration":
      baseTemplate.sections = [
        createSection("Personal Information", "Please provide your contact details", [
          createField("text", "Full Name", true),
          createField("email", "Email Address", true),
          createField("text", "Phone Number"),
          createField("text", "Company/Organization")
        ]),
        createSection("Event Details", "Select your preferences for the event", [
          createField("checkbox", "Which sessions would you like to attend?", false, [
            "Morning Keynote", 
            "Workshop A", 
            "Workshop B", 
            "Networking Lunch", 
            "Afternoon Panel"
          ]),
          createField("multipleChoice", "Dietary Requirements", false, [
            "No Restrictions", 
            "Vegetarian", 
            "Vegan", 
            "Gluten-Free", 
            "Other (Please Specify)"
          ]),
          createField("paragraph", "Any additional requests or information we should know?")
        ])
      ];
      break;
      
    case "research_study":
      baseTemplate.sections = [
        createSection("Demographics", "Please tell us about yourself", [
          createField("multipleChoice", "Age Range", true, [
            "18-24", 
            "25-34", 
            "35-44", 
            "45-54", 
            "55-64", 
            "65+"
          ]),
          createField("multipleChoice", "Gender", false, [
            "Male", 
            "Female", 
            "Non-binary", 
            "Prefer not to say"
          ]),
          createField("multipleChoice", "Education Level", true, [
            "High School", 
            "Bachelor's Degree", 
            "Master's Degree", 
            "PhD or Higher", 
            "Other"
          ])
        ]),
        createSection("Research Questions", "Please answer the following questions about the research topic", [
          createField("multipleChoice", "How familiar are you with this topic?", true, [
            "Very familiar", 
            "Somewhat familiar", 
            "Neutral", 
            "Somewhat unfamiliar", 
            "Very unfamiliar"
          ]),
          createField("paragraph", "What are your thoughts on this subject?"),
          createField("rating", "How important do you think this issue is?", true)
        ])
      ];
      break;
      
    default:
      // Generic template if no specific goal matches
      baseTemplate.sections = [
        createSection("General Information", "Please provide the following information", [
          createField("text", "Name", true),
          createField("email", "Email", true),
          createField("paragraph", "Your Feedback")
        ])
      ];
  }
  
  // Convert the partial survey to a full survey with IDs
  return createSurveyFromTemplate(baseTemplate as Partial<Survey>);
}

// Helper functions to create template structure
function createSurveyFromTemplate(templateSurvey: Partial<Survey>): Survey {
  // This would normally be handled by the backend
  // Here we're just creating a client-side representation
  return {
    id: crypto.randomUUID(),
    user_id: '', // This would be filled in by the SurveyContext
    title: templateSurvey.title || 'Untitled Survey',
    description: templateSurvey.description || '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    published: false,
    sections: templateSurvey.sections || [],
    responses: 0
  };
}

function createSection(title: string, description: string, fields: Field[] = []): Section {
  return {
    id: crypto.randomUUID(),
    survey_id: '',  // Will be set when added to survey
    title,
    description,
    order_index: 0, // Will be calculated when added to survey
    fields,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

function createField(
  type: FieldType, 
  question: string, 
  required: boolean = false, 
  options?: string[]
): Field {
  return {
    id: crypto.randomUUID(),
    section_id: '',  // Will be set when added to section
    type,
    question,
    required,
    options,
    order_index: 0,  // Will be calculated when added to section
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

function getDefaultTitle(goal: TemplateGoal): string {
  switch (goal) {
    case "customer_feedback":
      return "Customer Satisfaction Survey";
    case "event_registration":
      return "Event Registration Form";
    case "research_study":
      return "Research Study Questionnaire";
    case "employee_satisfaction":
      return "Employee Engagement Survey";
    case "education_assessment":
      return "Education Assessment Survey";
    case "product_evaluation":
      return "Product Evaluation Form";
    case "conference_feedback":
      return "Conference Feedback Survey";
    default:
      return "Survey Template";
  }
}

function getDefaultDescription(goal: TemplateGoal): string {
  switch (goal) {
    case "customer_feedback":
      return "Help us improve our products and services by sharing your feedback";
    case "event_registration":
      return "Please complete this form to register for our upcoming event";
    case "research_study":
      return "Thank you for participating in our research study";
    case "employee_satisfaction":
      return "We value your input on how we can make our workplace better";
    case "education_assessment":
      return "Please complete this assessment to help us evaluate learning outcomes";
    case "product_evaluation":
      return "Share your thoughts on our product to help us improve";
    case "conference_feedback":
      return "Tell us about your experience at our recent conference";
    default:
      return "Please complete this survey";
  }
}
