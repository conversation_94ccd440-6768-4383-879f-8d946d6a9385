import React, { useState } from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { MoreHorizontal, <PERSON><PERSON>l, Copy, Trash2, BarChart2 } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useSurvey } from '@/context/SurveyContext';
import { useToast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface SurveyCardProps {
  survey: {
    id: string;
    title: string;
    description?: string;
    created_at: string;
    published: boolean;
    share_link?: string;
    responses?: number;
  };
}

const SurveyCard: React.FC<SurveyCardProps> = ({ survey }) => {
  const navigate = useNavigate();
  const { deleteSurvey, publishSurvey, unpublish<PERSON>urvey, generateShareLink } = useSurvey();
  const { toast } = useToast();
  const [isCopied, setIsCopied] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteSurvey(survey.id);
      toast({
        title: "Survey Deleted",
        description: "Your survey has been successfully deleted.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete survey. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePublish = async () => {
    try {
      await publishSurvey(survey.id);
      toast({
        title: "Survey Published",
        description: "Your survey is now live and can be accessed by respondents.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to publish survey. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleUnpublish = async () => {
    try {
      await unpublishSurvey(survey.id);
      toast({
        title: "Survey Unpublished",
        description: "Your survey has been unpublished and is no longer accessible.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unpublish survey. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleGenerateShareLink = async () => {
    try {
      await generateShareLink(survey.id);
      toast({
        title: "Share Link Generated",
        description: "A unique share link has been generated for your survey.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate share link. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCopyToClipboard = () => {
    if (survey.share_link) {
      navigator.clipboard.writeText(`${window.location.origin}/s/${survey.share_link}`)
        .then(() => {
          setIsCopied(true);
          toast({
            title: "Link Copied",
            description: "The survey link has been copied to your clipboard.",
          });
          setTimeout(() => setIsCopied(false), 2000);
        })
        .catch(err => {
          console.error("Could not copy text: ", err);
          toast({
            title: "Error",
            description: "Failed to copy link. Please try again.",
            variant: "destructive",
          });
        });
    } else {
      toast({
        title: "No Share Link",
        description: "Please generate a share link first.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="bg-card text-card-foreground shadow-sm h-[420px] flex flex-col relative">
      <div className="absolute top-2 right-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => navigate(`/results/${survey.id}`)}>
              <BarChart2 className="h-4 w-4 mr-2" />
              View Results
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => navigate(`/create?id=${survey.id}`)}>
              <Pencil className="h-4 w-4 mr-2" />
              Edit Survey
            </DropdownMenuItem>
            {survey.share_link ? (
              <DropdownMenuItem onClick={handleCopyToClipboard} disabled={isCopied}>
                <Copy className="h-4 w-4 mr-2" />
                {isCopied ? "Copied!" : "Copy Share Link"}
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem onClick={handleGenerateShareLink}>
                <Copy className="h-4 w-4 mr-2" />
                Generate Share Link
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={handleDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Survey
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <CardHeader className="h-[100px]">
        <CardTitle className="text-lg font-semibold border-b-2 border-primary">{survey.title}</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">{survey.description?.substring(0, 45)}...</CardDescription>
      </CardHeader>
      
      <CardContent className="flex-grow flex flex-col">
        <div className="text-sm text-muted-foreground mb-4">
          Created on {format(new Date(survey.created_at), 'MMM dd, yyyy')}
        </div>
        <div className="mb-8">
          {survey.responses !== undefined ? (
            <span className="inline-flex items-center rounded-full bg-secondary-foreground/10 px-2.5 py-0.5 text-xs font-semibold text-secondary-foreground">
              {survey.responses} Responses
            </span>
          ) : (
            <span className="inline-flex items-center rounded-full bg-secondary-foreground/10 px-2.5 py-0.5 text-xs font-semibold text-secondary-foreground">
              No responses yet
            </span>
          )}
        </div>
        <div className="mt-auto">
        {survey.published && survey.share_link !== "" ? (
          <div className="space-y-1.5">
            <div className="flex items-center">
              <span className="text-xs font-medium text-muted-foreground">Shared Link</span>
              <span className="ml-2 inline-flex h-5 items-center rounded-full bg-green-100 px-2 text-xs font-medium text-green-700">Active</span>
            </div>
            <div className="relative flex items-center overflow-hidden rounded-md border bg-muted/40 group focus-within:ring-1 focus-within:ring-primary">
              <div className="flex-1 truncate px-3 py-2">
                <Link to={`/s/${survey.share_link}`} className="text-xs font-medium hover:text-primary truncate block">
                  {window.location.origin}/s/{survey.share_link}
                </Link>
              </div>
              <button 
                onClick={handleCopyToClipboard}
                className="flex h-full items-center justify-center bg-muted/80 px-3 py-2 text-xs font-medium transition-colors hover:bg-primary hover:text-primary-foreground"
                aria-label="Copy to clipboard"
              >
                <Copy className="h-3.5 w-3.5 mr-1.5" />
                Copy
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-1.5">
            <div className="flex items-center">
              <span className="text-xs font-medium text-muted-foreground">Shared Link</span>
              <span className="ml-2 inline-flex h-5 items-center rounded-full bg-amber-100 px-2 text-xs font-medium text-amber-700">Unavailable</span>
            </div>
            <div className="relative flex items-center overflow-hidden rounded-md border border-dashed bg-muted/20 py-2 px-3">
              <div className="flex flex-1 items-center text-xs text-muted-foreground truncate">
                <span>Shared link will be available after publishing the survey</span>
              </div>
            </div>
          </div>
        )}
        </div>
      </CardContent>
      
      <CardFooter className="mt-auto border-t pt-4">
        <div className="w-full flex justify-end">
          {survey.published ? (
            <Button variant="outline" size="sm" onClick={handleUnpublish}>
              Unpublish
            </Button>
          ) : (
            <Button size="sm" variant="default" onClick={handlePublish}>
              Publish
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default SurveyCard;
