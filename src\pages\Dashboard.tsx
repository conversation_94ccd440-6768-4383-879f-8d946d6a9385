import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, Search, Sparkles } from 'lucide-react';
import SurveyCard from '@/components/surveys/SurveyCard';
import { useSurvey } from '@/context/SurveyContext';
import PageTransition from '@/components/layout/PageTransition';
const Dashboard = () => {
  const {
    surveys
  } = useSurvey();
  const [searchQuery, setSearchQuery] = useState('');
  const publishedSurveys = surveys.filter(survey => survey.published);
  const draftSurveys = surveys.filter(survey => !survey.published);
  const filteredSurveys = surveys.filter(survey => survey.title.toLowerCase().includes(searchQuery.toLowerCase()));
  const filteredPublished = publishedSurveys.filter(survey => survey.title.toLowerCase().includes(searchQuery.toLowerCase()));
  const filteredDrafts = draftSurveys.filter(survey => survey.title.toLowerCase().includes(searchQuery.toLowerCase()));
  return <div className="min-h-screen flex flex-col bg-background">
      <Navbar />
      <PageTransition>
        <main className="flex-1 container mx-auto px-4 py-8 max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
            <div>
              <h1 className="text-3xl font-bold">surveys</h1>
              <p className="text-muted-foreground">Manage your surveys and view responses</p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search surveys..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="pl-10" />
              </div>
              
              <div className="flex gap-2">
                <Link to="/templates">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <Sparkles className="mr-2 h-4 w-4" />
                    Templates
                  </Button>
                </Link>
                
                <Link to="/create">
                  <Button className="w-full sm:w-auto">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    New Survey
                  </Button>
                </Link>
              </div>
            </div>
          </div>
          
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="all">
                All Surveys <span className="ml-2 bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs">{filteredSurveys.length}</span>
              </TabsTrigger>
              <TabsTrigger value="published">
                Published <span className="ml-2 bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs">{filteredPublished.length}</span>
              </TabsTrigger>
              <TabsTrigger value="drafts">
                Drafts <span className="ml-2 bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs">{filteredDrafts.length}</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="all">
              {filteredSurveys.length === 0 ? <EmptyState query={searchQuery} /> : <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredSurveys.map(survey => <SurveyCard key={survey.id} survey={survey} />)}
                </div>}
            </TabsContent>
            
            <TabsContent value="published">
              {filteredPublished.length === 0 ? <EmptyState query={searchQuery} type="published" /> : <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredPublished.map(survey => <SurveyCard key={survey.id} survey={survey} />)}
                </div>}
            </TabsContent>
            
            <TabsContent value="drafts">
              {filteredDrafts.length === 0 ? <EmptyState query={searchQuery} type="drafts" /> : <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredDrafts.map(survey => <SurveyCard key={survey.id} survey={survey} />)}
                </div>}
            </TabsContent>
          </Tabs>
        </main>
      </PageTransition>
    </div>;
};
const EmptyState = ({
  query,
  type = "all"
}: {
  query: string;
  type?: string;
}) => {
  if (query) {
    return <div className="text-center py-16 border border-dashed rounded-lg">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
          <Search className="h-6 w-6 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">No results found</h3>
        <p className="text-muted-foreground mb-6">
          No {type === "all" ? "surveys" : type} match your search "{query}"
        </p>
        <div className="flex justify-center">
          <Button variant="outline" onClick={() => window.location.reload()}>
            Clear Search
          </Button>
        </div>
      </div>;
  }
  return <div className="text-center py-16 border border-dashed rounded-lg">
      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
        {type === "published" ? <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-muted-foreground">
            <path d="M12 2.69l5.66 5.66a8 8 0 11-11.31 0z" />
          </svg> : type === "drafts" ? <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-muted-foreground">
            <path d="M12 20h9" />
            <path d="M16.5 3.5a2.12 2.12 0 013 3L7 19l-4 1 1-4L16.5 3.5z" />
          </svg> : <PlusCircle className="h-6 w-6 text-muted-foreground" />}
      </div>
      <h3 className="text-lg font-medium mb-2">
        {type === "published" ? "No published surveys" : type === "drafts" ? "No draft surveys" : "No surveys yet"}
      </h3>
      <p className="text-muted-foreground mb-6">
        {type === "published" ? "Publish a survey to see it here" : type === "drafts" ? "Create a survey to get started" : "Create your first survey now"}
      </p>
      <div className="flex justify-center">
        <div className="flex flex-col sm:flex-row gap-3">
          <Link to="/templates">
            <Button variant="outline" className="w-full">
              <Sparkles className="mr-2 h-4 w-4" />
              Use AI Templates
            </Button>
          </Link>
          <Link to="/create">
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Survey
            </Button>
          </Link>
        </div>
      </div>
    </div>;
};
export default Dashboard;